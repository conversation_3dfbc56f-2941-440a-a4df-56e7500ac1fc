'use client';

import React, { useEffect, useState } from 'react';

interface Category {
  id: string;
  name: string;
}

interface CategorySelectorProps {
  selectedValue: string;
  onValueChange: (value: string) => void;
  disabled?: boolean;
  categories?: Category[];
}

/**
 * CategorySelector component provides a dropdown to select a category.
 * It fetches available categories and acts as a controlled component.
 */
export default function CategorySelector({
  selectedValue,
  onValueChange,
  disabled = false,
  categories: initialCategories,
}: CategorySelectorProps) {
  const [categories, setCategories] = useState<Category[]>(initialCategories || []);
  const [loading, setLoading] = useState(!initialCategories);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchCategories() {
      if (initialCategories) return;

      setLoading(true);
      setError(null);
      try {
        const res = await fetch('/api/categories/get');
        if (!res.ok) {
          throw new Error('Failed to fetch categories');
        }
        const data = await res.json();
        setCategories(data.categories || []);
      } catch (err) {
        setError((err as Error).message);
      } finally {
        setLoading(false);
      }
    }
    fetchCategories();
  }, [initialCategories]);

  if (loading) {
    return <div>Loading categories...</div>;
  }

  if (error) {
    return <div className='text-red-600 text-sm'>Error: {error}</div>;
  }

  return (
    <select
      value={selectedValue || ''}
      onChange={(e) => onValueChange(e.target.value)}
      onClick={(e) => e.stopPropagation()}
      disabled={disabled || loading}
      className='block w-full rounded-md border border-gray-300 bg-white py-2 px-3 text-sm focus:border-indigo-500 focus:ring-indigo-500'
    >
      <option value=''>Select a category</option>
      {categories.map((cat) => (
        <option key={cat.id} value={cat.id}>
          {cat.name}
        </option>
      ))}
    </select>
  );
}
