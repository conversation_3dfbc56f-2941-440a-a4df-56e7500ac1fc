import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import {
  getFinancialAccountCredentials,
  fetchPlaidTransactions,
  handleRemovedTransactions,
  updateAccountSyncMetadata,
} from '@/lib/services/transactionService';
import { RemovedTransaction, Transaction } from 'plaid';

interface SyncTransactionsRequest {
  item_id: string;
}

interface PlaidMetadata {
  access_token: string;
  cursor?: string;
  [key: string]: unknown;
}

interface PlaidSyncResponse {
  added: Transaction[];
  modified: Transaction[];
  removed: RemovedTransaction[];
  nextCursor: string;
  error?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: SyncTransactionsRequest = await request.json();
    const { item_id } = body;

    if (!item_id) {
      return NextResponse.json({ error: 'Missing required field: item_id' }, { status: 400 });
    }

    const supabase = await createSupabaseServerClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const credentials = await getFinancialAccountCredentials(supabase, user.id, item_id);
    const plaidResponse: PlaidSyncResponse = await fetchPlaidTransactions(
      credentials.accessToken,
      credentials.cursor
    );

    if (plaidResponse.error) {
      throw new Error('Plaid API error');
    }

    const { data: syncResult, error: syncError } = await supabase.rpc('handle_transaction_sync', {
      p_user_id: user.id,
      p_account_id: credentials.accountId,
      p_added_transactions: plaidResponse.added,
      p_modified_transactions: plaidResponse.modified,
    });

    if (syncError) {
      throw new Error('Failed to upsert transaction: DB upsert failed');
    }

    if (plaidResponse.removed.length > 0) {
      await handleRemovedTransactions(supabase, plaidResponse.removed, user.id);
    }

    const { data: accountData } = await supabase
      .from('financial_accounts')
      .select('plaid_metadata')
      .eq('id', credentials.accountId)
      .single();

    if (accountData?.plaid_metadata) {
      await updateAccountSyncMetadata(
        supabase,
        credentials.accountId,
        accountData.plaid_metadata as PlaidMetadata,
        plaidResponse.nextCursor
      );
    }

    return NextResponse.json({
      success: true,
      summary: {
        ...syncResult,
        removed: plaidResponse.removed.length,
        next_cursor: plaidResponse.nextCursor,
      },
    });
  } catch (error) {
    console.error('[PLAID_SYNC_TRANSACTIONS_ERROR]', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';

    if (errorMessage.includes('Financial account not found')) {
      return NextResponse.json({ error: errorMessage }, { status: 404 });
    }

    if (errorMessage.includes('Access token not found')) {
      return NextResponse.json(
        { error: 'Access token not found in account metadata' },
        { status: 500 }
      );
    }

    if (errorMessage.includes('Failed to upsert transaction')) {
      return NextResponse.json(
        { error: 'Failed to upsert transaction: DB upsert failed' },
        { status: 500 }
      );
    }

    if (errorMessage.includes('Failed to mark transaction as removed')) {
      return NextResponse.json(
        { error: 'Failed to mark transaction as removed: DB update failed' },
        { status: 500 }
      );
    }

    if (errorMessage.includes('Failed to update sync cursor')) {
      return NextResponse.json(
        { error: 'Failed to update sync cursor: DB update failed' },
        { status: 500 }
      );
    }

    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
