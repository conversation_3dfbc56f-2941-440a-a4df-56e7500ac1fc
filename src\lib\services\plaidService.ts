import { createSupabaseServerClient } from '@/lib/supabase/server';

interface FinancialAccountData {
  userId: string;
  accessToken: string;
  itemId: string;
  institutionName: string;
  institutionId: string;
}

export async function createFinancialAccount(data: FinancialAccountData) {
  const supabase = await createSupabaseServerClient();
  const { userId, accessToken, itemId, institutionName, institutionId } = data;

  const { error: insertError } = await supabase.from('financial_accounts').insert({
    user_id: userId,
    plaid_item_id: itemId,
    plaid_account_id: `temp_${itemId}`,
    account_name: `${institutionName} Account`,
    account_type: 'other',
    institution_name: institutionName,
    institution_id: institutionId,
    plaid_metadata: {
      access_token: accessToken,
      item_id: itemId,
      institution: {
        name: institutionName,
        institution_id: institutionId,
      },
      exchange_timestamp: new Date().toISOString(),
    },
  });

  if (insertError) {
    console.error('Database insertion error:', insertError);
    throw new Error('Failed to store account credentials');
  }

  return { ok: true };
}
