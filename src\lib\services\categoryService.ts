import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';

type SupabaseClientType = SupabaseClient<Database>;

export class CategoryDeletionError extends Error {
  status: number;
  details?: string;

  constructor(message: string, status: number, details?: string) {
    super(message);
    this.name = 'CategoryDeletionError';
    this.status = status;
    this.details = details;
  }
}

export async function deleteCategory(
  supabase: SupabaseClientType,
  categoryId: string,
  userId: string
) {
  // First, verify the category exists and belongs to the user
  const { data: existingCategory, error: fetchError } = await supabase
    .from('user_categories')
    .select('id, name')
    .eq('id', categoryId)
    .eq('user_id', userId)
    .single();

  if (fetchError || !existingCategory) {
    throw new CategoryDeletionError('Category not found or access denied', 404);
  }

  // Check if the category is being used by any transactions
  const { count: transactionCount, error: countError } = await supabase
    .from('transactions')
    .select('*', { count: 'exact', head: true })
    .eq('user_category_id', categoryId)
    .eq('user_id', userId);

  if (countError) {
    console.error('Error checking transaction usage:', countError);
    throw new CategoryDeletionError('Failed to verify category usage', 500);
  }

  // If category is in use, prevent deletion
  if (transactionCount && transactionCount > 0) {
    throw new CategoryDeletionError(
      'Cannot delete category that is assigned to transactions',
      409,
      `This category is currently assigned to ${transactionCount} transaction(s)`
    );
  }

  // Delete the category
  const { error: deleteError } = await supabase
    .from('user_categories')
    .delete()
    .eq('id', categoryId)
    .eq('user_id', userId);

  if (deleteError) {
    console.error('Error deleting category:', deleteError);
    throw new CategoryDeletionError('Failed to delete category', 500);
  }

  return {
    id: categoryId,
    name: existingCategory.name,
  };
}
