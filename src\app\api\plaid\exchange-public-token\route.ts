import { NextRequest, NextResponse } from 'next/server';
import { plaidClient } from '@/lib/plaid';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { createFinancialAccount } from '@/lib/services/plaidService';

interface ExchangeTokenRequest {
  public_token: string;
  metadata: {
    institution: {
      institution_id: string;
      name: string;
    };
  };
}

export async function POST(request: NextRequest) {
  try {
    const body: ExchangeTokenRequest = await request.json();
    const { public_token, metadata } = body;

    if (!public_token || !metadata?.institution) {
      return NextResponse.json(
        { error: 'Missing required fields: public_token and metadata.institution' },
        { status: 400 }
      );
    }

    const supabase = await createSupabaseServerClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const exchangeResponse = await plaidClient.itemPublicTokenExchange({
      public_token,
    });

    const { access_token, item_id } = exchangeResponse.data;

    await createFinancialAccount({
      userId: user.id,
      accessToken: access_token,
      itemId: item_id,
      institutionName: metadata.institution.name,
      institutionId: metadata.institution.institution_id,
    });

    return NextResponse.json({ ok: true });
  } catch (error) {
    console.error('[PLAID_EXCHANGE_PUBLIC_TOKEN_ERROR]', error);
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
