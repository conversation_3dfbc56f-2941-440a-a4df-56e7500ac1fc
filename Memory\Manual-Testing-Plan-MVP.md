# Manual Testing Plan: NAVsync.io MVP

## 1. Introduction

This document outlines the manual testing plan for the NAVsync.io Minimum Viable Product (MVP). The purpose of this plan is to provide a clear, step-by-step guide for non-technical users to test the core functionality of the application.

**Objective:** To systematically verify all MVP features, identify bugs, and ensure the application meets the success criteria outlined in the development plan.

**Testing Environment:**

- **URL:** [Link to the deployed test application]
- **Browser:** Google Chrome (Latest Version)
- **Credentials:** Use the provided test accounts or create a new one.

**How to Use This Document:**

1.  Follow the steps for each test case exactly as described.
2.  Compare the actual result with the "Expected Outcome."
3.  Mark the test case as "Pass" or "Fail" in the "Status" column.
4.  If a test fails, please provide a brief description of what happened in the "Notes" section.

---

## 2. User Journey 1: New User Onboarding

This section tests the complete user registration and login process.

| Test Case ID | Feature                 | Test Steps                                                                                                                                                                 | Expected Outcome                                                                                 | Status (Pass/Fail) | Notes |
| :----------- | :---------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------- | :----------------- | :---- |
| **TC-001**   | **Account Creation**    | 1. Navigate to the application's home page. <br> 2. Click the "Sign Up" button. <br> 3. Enter a valid email address and a strong password. <br> 4. Click "Create Account". | The user is successfully registered and redirected to a confirmation page or the main dashboard. | [ ] Pass [ ] Fail  |       |
| **TC-002**   | **Email Verification**  | 1. After signing up, check the inbox for the email address used. <br> 2. Open the verification email from NAVsync.io. <br> 3. Click the verification link.                 | The user is taken to an "Email Confirmed" page and their account is marked as verified.          | [ ] Pass [ ] Fail  |       |
| **TC-003**   | **User Login**          | 1. Navigate to the login page. <br> 2. Enter the credentials of the newly created and verified account. <br> 3. Click "Log In".                                            | The user is successfully logged in and redirected to the main dashboard.                         | [ ] Pass [ ] Fail  |       |
| **TC-004**   | **Initial Setup Guide** | 1. Upon first login, observe the screen.                                                                                                                                   | The user is greeted with a welcome message and guided to connect their first bank account.       | [ ] Pass [ ] Fail  |       |

---

## 3. User Journey 2: Bank Account Connection

This section tests the Plaid integration for connecting financial accounts.

| Test Case ID | Feature                      | Test Steps                                                                                                                                                                                                                                                        | Expected Outcome                                                                                                               | Status (Pass/Fail) | Notes |
| :----------- | :--------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------- | :----------------- | :---- |
| **TC-005**   | **Initiate Plaid Link**      | 1. From the dashboard, click the "Connect Bank Account" button.                                                                                                                                                                                                   | The Plaid Link modal window opens, prompting the user to select their financial institution.                                   | [ ] Pass [ ] Fail  |       |
| **TC-006**   | **Connect Bank Account**     | 1. In the Plaid modal, select a test institution (e.g., "Chase"). <br> 2. Use the provided test credentials to log in. <br> 3. Complete any multi-factor authentication steps. <br> 4. Select an account to connect (e.g., "Checking"). <br> 5. Click "Continue". | The Plaid modal closes, and a success message appears. The connected account is now visible on the dashboard or accounts page. | [ ] Pass [ ] Fail  |       |
| **TC-007**   | **Verify Account Display**   | 1. Navigate to the "Accounts" page.                                                                                                                                                                                                                               | The newly connected bank account is listed with the correct name, type, and current balance.                                   | [ ] Pass [ ] Fail  |       |
| **TC-008**   | **Initial Transaction Sync** | 1. Navigate to the "Transactions" page.                                                                                                                                                                                                                           | Transactions from the connected account begin to appear in the transaction list within a few minutes.                          | [ ] Pass [ ] Fail  |       |

---

## 4. User Journey 3: Transaction Management

This section tests the viewing, categorization, and filtering of transactions.

| Test Case ID | Feature                      | Test Steps                                                                                                                                     | Expected Outcome                                                                                                           | Status (Pass/Fail) | Notes |
| :----------- | :--------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------- | :----------------- | :---- |
| **TC-009**   | **View Transactions**        | 1. Go to the "Transactions" page.                                                                                                              | A list of transactions is displayed, showing the date, merchant name, category, and amount for each.                       | [ ] Pass [ ] Fail  |       |
| **TC-010**   | **View Transaction Details** | 1. Click on a single transaction from the list.                                                                                                | A modal or side panel opens showing more details about the transaction, such as the original description and account name. | [ ] Pass [ ] Fail  |       |
| **TC-011**   | **Manual Categorization**    | 1. Find an "Uncategorized" transaction. <br> 2. Click the category dropdown next to it. <br> 3. Select a new category (e.g., "Food & Dining"). | The transaction's category is updated immediately in the list.                                                             | [ ] Pass [ ] Fail  |       |
| **TC-012**   | **Filter Transactions**      | 1. Use the filter controls on the "Transactions" page. <br> 2. Filter by a specific date range. <br> 3. Filter by a specific category.         | The transaction list updates to show only the transactions that match the selected filter criteria.                        | [ ] Pass [ ] Fail  |       |
| **TC-013**   | **Search Transactions**      | 1. Use the search bar on the "Transactions" page. <br> 2. Type the name of a merchant (e.g., "Starbucks").                                     | The transaction list updates to show only transactions matching the search term.                                           | [ ] Pass [ ] Fail  |       |

---

## 5. User Journey 4: Budget Creation & Tracking

This section tests the core budgeting functionality.

| Test Case ID | Feature                       | Test Steps                                                                                                                                                                                          | Expected Outcome                                                                                                              | Status (Pass/Fail) | Notes |
| :----------- | :---------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------- | :----------------- | :---- |
| **TC-014**   | **Create Monthly Budget**     | 1. Navigate to the "Budgets" page. <br> 2. Click "Create New Budget". <br> 3. Select a category (e.g., "Groceries"). <br> 4. Enter a monthly budget amount (e.g., "$500"). <br> 5. Save the budget. | The new budget for "Groceries" appears in the budget list with the correct amount.                                            | [ ] Pass [ ] Fail  |       |
| **TC-015**   | **Verify Budget Categories**  | 1. When creating a budget, view the list of available categories.                                                                                                                                   | The categories available for budgeting match the transaction categories.                                                      | [ ] Pass [ ] Fail  |       |
| **TC-016**   | **Real-time Spending Update** | 1. Find a transaction and categorize it under a budgeted category (e.g., categorize a $50 transaction as "Groceries"). <br> 2. Navigate to the "Budgets" page.                                      | The "Groceries" budget now shows $50 spent out of the $500 total, and the progress bar is updated.                            | [ ] Pass [ ] Fail  |       |
| **TC-017**   | **Visual Budget Progress**    | 1. Observe the budget items on the "Budgets" page.                                                                                                                                                  | Each budget item has a clear visual indicator (like a progress bar) showing the percentage of the budget that has been spent. | [ ] Pass [ ] Fail  |       |

---

## 6. User Journey 5: Dashboard Overview

This section tests the main dashboard for accuracy and functionality.

| Test Case ID | Feature                         | Test Steps                                                                                                               | Expected Outcome                                                                                                   | Status (Pass/Fail) | Notes |
| :----------- | :------------------------------ | :----------------------------------------------------------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------- | :----------------- | :---- |
| **TC-018**   | **Dashboard Loading**           | 1. Log in to the application.                                                                                            | The main dashboard loads completely in under 3 seconds without errors.                                             | [ ] Pass [ ] Fail  |       |
| **TC-019**   | **Financial Overview Accuracy** | 1. Compare the "Total Balance" or "Net Worth" figure on the dashboard with the sum of balances from the "Accounts" page. | The financial overview metrics on the dashboard are accurate and reflect the correct data from connected accounts. | [ ] Pass [ ] Fail  |       |
| **TC-020**   | **Recent Transactions Display** | 1. Look at the "Recent Transactions" widget on the dashboard.                                                            | The widget displays the 5-10 most recent transactions correctly.                                                   | [ ] Pass [ ] Fail  |       |
| **TC-021**   | **Dashboard Navigation**        | 1. Click on the navigation links for "Transactions", "Budgets", and "Accounts" from the main menu.                       | The user is correctly navigated to the respective pages.                                                           | [ ] Pass [ ] Fail  |       |
