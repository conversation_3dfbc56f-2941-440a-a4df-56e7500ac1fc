'use client';

import * as React from 'react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useAuth } from '@/lib/hooks/useAuth';

// Schema with z.refine for real-time password matching validation
const signUpSchema = z
  .object({
    email: z.string().min(1, 'Email is required').email('Invalid email address'),
    password: z.string().min(6, 'Password must be at least 6 characters'),
    confirmPassword: z.string().min(1, 'Please confirm your password'),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });

type SignUpFormData = z.infer<typeof signUpSchema>;

export function SignUpForm() {
  const { signUp, isLoading, error } = useAuth();
  const [success, setSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<SignUpFormData>({
    resolver: zodResolver(signUpSchema),
    mode: 'onBlur',
    reValidateMode: 'onChange',
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  const onSubmit = async (data: SignUpFormData) => {
    const result = await signUp(data);
    if (result && !result.error) {
      setSuccess(true);
      reset();
    }
  };

  return (
    <div className='flex justify-center items-center min-h-[60vh]'>
      <Card className='w-full max-w-md p-6 shadow-lg'>
        <h2 className='text-2xl font-semibold mb-6 text-center'>Create your NAVsync.io account</h2>
        <form onSubmit={handleSubmit(onSubmit)} className='space-y-5' noValidate>
          <div>
            <label htmlFor='email' className='block text-sm font-medium mb-1'>
              Email
            </label>
            <Input
              id='email'
              type='email'
              autoComplete='email'
              placeholder='<EMAIL>'
              disabled={isLoading}
              {...register('email')}
            />
            {errors.email && <p className='text-red-600 text-xs mt-1'>{errors.email.message}</p>}
          </div>

          <div>
            <label htmlFor='password' className='block text-sm font-medium mb-1'>
              Password
            </label>
            <Input
              id='password'
              type='password'
              autoComplete='new-password'
              placeholder='Enter password'
              disabled={isLoading}
              {...register('password')}
            />
            {errors.password && (
              <p className='text-red-600 text-xs mt-1'>{errors.password.message}</p>
            )}
          </div>

          <div>
            <label htmlFor='confirmPassword' className='block text-sm font-medium mb-1'>
              Confirm Password
            </label>
            <Input
              id='confirmPassword'
              type='password'
              autoComplete='new-password'
              placeholder='Re-enter password'
              disabled={isLoading}
              {...register('confirmPassword')}
            />
            {errors.confirmPassword && (
              <p className='text-red-600 text-xs mt-1'>{errors.confirmPassword.message}</p>
            )}
          </div>

          {error && <div className='text-red-600 text-sm text-center'>{error.message}</div>}

          {success && !isLoading && !error && (
            <div className='text-green-600 text-sm text-center'>
              Account created successfully! Please check your email to verify your account.
            </div>
          )}

          <Button type='submit' className='w-full' disabled={isLoading}>
            {isLoading ? (
              <span>
                <span className='animate-spin inline-block mr-2'>&#9696;</span>
                Creating account...
              </span>
            ) : (
              'Sign Up'
            )}
          </Button>
        </form>
      </Card>
    </div>
  );
}

export default SignUpForm;
