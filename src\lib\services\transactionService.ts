import { plaidClient } from '@/lib/plaid';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { TransactionsSyncRequest, Transaction, RemovedTransaction } from 'plaid';
import { Database } from '@/lib/supabase/database.types';

type SupabaseClient = Awaited<ReturnType<typeof createSupabaseServerClient>>;
type TransactionInsert = Database['public']['Tables']['transactions']['Insert'];

interface PlaidMetadata {
  access_token: string;
  cursor?: string;
  [key: string]: unknown;
}

interface FinancialAccountCredentials {
  accountId: string;
  accessToken: string;
  cursor?: string;
}

interface PlaidSyncResponse {
  added: Transaction[];
  modified: Transaction[];
  removed: RemovedTransaction[];
  nextCursor: string;
}

/**
 * Fetches the financial account credentials (access token and cursor) for a given item_id
 */
export async function getFinancialAccountCredentials(
  supabase: SupabaseClient,
  userId: string,
  itemId: string
): Promise<FinancialAccountCredentials> {
  const { data: accountData, error: accountError } = await supabase
    .from('financial_accounts')
    .select('id, plaid_metadata')
    .eq('user_id', userId)
    .eq('plaid_item_id', itemId)
    .single();

  if (accountError || !accountData) {
    throw new Error('Financial account not found for this item_id');
  }

  const plaidMetadata = accountData.plaid_metadata as PlaidMetadata;
  const accessToken = plaidMetadata.access_token;

  if (!accessToken) {
    throw new Error('Access token not found in account metadata');
  }

  return {
    accountId: accountData.id,
    accessToken,
    cursor: plaidMetadata.cursor,
  };
}

/**
 * Fetches transactions from Plaid using the transactionsSync API
 */
export async function fetchPlaidTransactions(
  accessToken: string,
  cursor?: string
): Promise<PlaidSyncResponse> {
  const syncRequest: TransactionsSyncRequest = {
    access_token: accessToken,
  };

  if (cursor) {
    syncRequest.cursor = cursor;
  }

  const syncResponse = await plaidClient.transactionsSync(syncRequest);
  const { added, modified, removed, next_cursor } = syncResponse.data;

  return {
    added,
    modified,
    removed,
    nextCursor: next_cursor,
  };
}

/**
 * Maps a Plaid transaction to our database schema
 */
function mapPlaidTransactionToDatabase(
  transaction: Transaction,
  userId: string,
  accountId: string
): TransactionInsert {
  return {
    user_id: userId,
    account_id: accountId,
    plaid_transaction_id: transaction.transaction_id,
    amount: Math.abs(transaction.amount), // Plaid amounts are negative for debits
    currency_code: transaction.iso_currency_code || 'USD',
    transaction_date: transaction.date,
    authorized_date: transaction.authorized_date || null,
    posted_date: transaction.date, // Use date as posted_date if no specific posted date
    merchant_name: transaction.merchant_name || null,
    description: transaction.name,
    plaid_category: transaction.category || null,
    plaid_category_detailed: transaction.category ? transaction.category.join(' > ') : null,
    account_owner: transaction.account_owner || null,
    transaction_type: transaction.transaction_type || null,
    transaction_code: transaction.transaction_code || null,
    location: transaction.location ? JSON.parse(JSON.stringify(transaction.location)) : null,
    is_pending: transaction.pending || false,
    status: transaction.pending ? 'pending' : 'posted',
    plaid_metadata: {
      original_description: transaction.original_description,
      datetime: transaction.datetime,
      authorized_datetime: transaction.authorized_datetime,
      payment_meta: transaction.payment_meta
        ? JSON.parse(JSON.stringify(transaction.payment_meta))
        : null,
      personal_finance_category: transaction.personal_finance_category
        ? JSON.parse(JSON.stringify(transaction.personal_finance_category))
        : null,
    },
    updated_at: new Date().toISOString(),
  };
}

/**
 * Handles upserting (insert or update) transactions in the database
 */
export async function upsertTransactions(
  supabase: SupabaseClient,
  transactions: Transaction[],
  userId: string,
  accountId: string
): Promise<void> {
  for (const transaction of transactions) {
    const transactionData = mapPlaidTransactionToDatabase(transaction, userId, accountId);

    const { error: upsertError } = await supabase.from('transactions').upsert(transactionData, {
      onConflict: 'user_id,plaid_transaction_id',
      ignoreDuplicates: false,
    });

    if (upsertError) {
      console.error('Error upserting transaction:', upsertError);
      throw new Error(`Failed to upsert transaction: ${upsertError.message}`);
    }
  }
}

/**
 * Handles marking removed transactions as removed in the database
 */
export async function handleRemovedTransactions(
  supabase: SupabaseClient,
  removedTransactions: RemovedTransaction[],
  userId: string
): Promise<void> {
  for (const removedTransaction of removedTransactions) {
    const { error: removeError } = await supabase
      .from('transactions')
      .update({ status: 'removed', updated_at: new Date().toISOString() })
      .eq('user_id', userId)
      .eq('plaid_transaction_id', removedTransaction.transaction_id);

    if (removeError) {
      console.error('Error marking transaction as removed:', removeError);
      throw new Error(`Failed to mark transaction as removed: ${removeError.message}`);
    }
  }
}

/**
 * Updates the financial account's metadata with the new cursor and sync timestamp
 */
export async function updateAccountSyncMetadata(
  supabase: SupabaseClient,
  accountId: string,
  currentMetadata: PlaidMetadata,
  nextCursor: string
): Promise<void> {
  const updatedMetadata = {
    ...currentMetadata,
    cursor: nextCursor,
    last_sync: new Date().toISOString(),
  };

  const { error: updateError } = await supabase
    .from('financial_accounts')
    .update({
      plaid_metadata: updatedMetadata,
      last_synced_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })
    .eq('id', accountId);

  if (updateError) {
    console.error('Error updating cursor:', updateError);
    throw new Error(`Failed to update sync cursor: ${updateError.message}`);
  }
}
interface TransactionFilters {
  searchQuery?: string;
  categoryId?: string;
  startDate?: string;
  endDate?: string;
}

/**
 * Fetches transactions with pagination, filtering, and total count.
 */
export async function getTransactions(
  supabase: SupabaseClient,
  userId: string,
  page: number,
  pageSize: number,
  filters?: TransactionFilters
) {
  const offset = (page - 1) * pageSize;

  // Build the base query for counting
  let countQuery = supabase
    .from('transactions')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', userId);

  // Build the base query for fetching transactions
  let transactionsQuery = supabase
    .from('transactions')
    .select(
      `
      id,
      amount,
      currency_code,
      transaction_date,
      authorized_date,
      posted_date,
      merchant_name,
      description,
      category_id,
      user_category_id,
      plaid_category,
      plaid_category_detailed,
      transaction_type,
      location,
      is_pending,
      is_recurring,
      status,
      tags,
      notes,
      created_at,
      updated_at,
      financial_accounts!inner (
        id,
        account_name,
        institution_name,
        account_type,
        account_subtype,
        mask
      )
    `
    )
    .eq('user_id', userId);

  // Apply filters if provided
  if (filters) {
    // Search by merchant name (case-insensitive)
    if (filters.searchQuery) {
      const searchPattern = `%${filters.searchQuery}%`;
      countQuery = countQuery.ilike('merchant_name', searchPattern);
      transactionsQuery = transactionsQuery.ilike('merchant_name', searchPattern);
    }

    // Filter by category
    if (filters.categoryId) {
      countQuery = countQuery.eq('user_category_id', filters.categoryId);
      transactionsQuery = transactionsQuery.eq('user_category_id', filters.categoryId);
    }

    // Filter by date range
    if (filters.startDate) {
      countQuery = countQuery.gte('transaction_date', filters.startDate);
      transactionsQuery = transactionsQuery.gte('transaction_date', filters.startDate);
    }

    if (filters.endDate) {
      countQuery = countQuery.lte('transaction_date', filters.endDate);
      transactionsQuery = transactionsQuery.lte('transaction_date', filters.endDate);
    }
  }

  // Get total count for pagination
  const { count, error: countError } = await countQuery;

  if (countError) {
    console.error('Error counting transactions:', countError);
    throw new Error('Failed to count transactions');
  }

  // Fetch transactions with applied filters, ordering, and pagination
  const { data: transactions, error: transactionsError } = await transactionsQuery
    .order('transaction_date', { ascending: false })
    .order('created_at', { ascending: false })
    .range(offset, offset + pageSize - 1);

  if (transactionsError) {
    console.error('Error fetching transactions:', transactionsError);
    throw new Error('Failed to fetch transactions');
  }

  return {
    transactions: transactions || [],
    totalCount: count || 0,
  };
}

/**
 * Bulk updates the category for multiple transactions.
 *
 * @param supabase - The Supabase client instance.
 * @param transactionIds - An array of transaction UUIDs to update.
 * @param categoryId - The UUID of the new user category.
 */
export async function bulkUpdateTransactionCategory(
  supabase: SupabaseClient,
  transactionIds: string[],
  categoryId: string
) {
  const { error } = await supabase
    .from('transactions')
    .update({ user_category_id: categoryId, updated_at: new Date().toISOString() })
    .in('id', transactionIds);

  if (error) {
    console.error('Error bulk updating transaction category:', error);
    throw new Error('Failed to bulk update transaction category');
  }
}
