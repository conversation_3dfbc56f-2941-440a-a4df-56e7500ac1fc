'use client';

import React from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import PlaidLink from '@/components/plaid/PlaidLink';
import { useAuth } from './AuthProvider';

interface OnboardingGuideProps {
  refetch: () => Promise<void>;
}

export default function OnboardingGuide({ refetch }: OnboardingGuideProps) {
  const { user } = useAuth();

  if (!user) {
    return null;
  }

  const handleSuccess = async () => {
    await refetch();
  };

  return (
    <Card className='w-full max-w-md'>
      <CardHeader>
        <CardTitle>Welcome to NAVsync.io!</CardTitle>
        <CardDescription>
          Let&amp;apos;s get you started. The first step is to securely connect your bank account to
          begin tracking your finances.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <PlaidLink userId={user.id} onSuccess={handleSuccess} />
      </CardContent>
    </Card>
  );
}
