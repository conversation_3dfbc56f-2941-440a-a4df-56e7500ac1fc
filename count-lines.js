import fs from 'fs';
import path from 'path';

const IGNORED_DIRS = ['node_modules', '.next', '.vscode', '__tests__'];
const ALLOWED_EXTENSIONS = ['.ts', '.tsx', '.js', '.mjs', '.sql'];

function countLinesInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf-8');
    return content.split('\n').length;
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error);
    return 0;
  }
}

function walkDir(dir, totalLines) {
  try {
    const files = fs.readdirSync(dir);
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        if (!IGNORED_DIRS.includes(file)) {
          totalLines = walkDir(filePath, totalLines);
        }
      } else {
        const ext = path.extname(filePath);
        if (ALLOWED_EXTENSIONS.includes(ext)) {
          totalLines += countLinesInFile(filePath);
        }
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error);
  }
  return totalLines;
}

function main() {
  const dirs = process.argv.slice(2);
  if (dirs.length === 0) {
    console.log('Please provide at least one directory to scan.');
    console.log('Usage: node count-lines.js <dir1> <dir2> ...');
    return;
  }

  let totalLines = 0;
  for (const dir of dirs) {
    const fullPath = path.resolve(dir);
    if (fs.existsSync(fullPath) && fs.statSync(fullPath).isDirectory()) {
      totalLines = walkDir(fullPath, totalLines);
    } else {
      console.warn(`Directory not found or is not a directory: ${fullPath}`);
    }
  }

  console.log(`Total lines of code: ${totalLines}`);
}

main();
