'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Rule } from './RuleForm';

interface Category {
  id: string;
  name: string;
}

interface RuleListProps {
  rules: Rule[];
  categories: Category[];
  onEdit: (rule: Rule) => void;
  onDelete: (id: string) => void;
}

const RuleList: React.FC<RuleListProps> = ({ rules, categories, onEdit, onDelete }) => {
  const getCategoryName = (categoryId: string) => {
    const category = categories.find((c) => c.id === categoryId);
    return category ? category.name : 'N/A';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Categorization Rules</CardTitle>
      </CardHeader>
      <CardContent>
        <div className='space-y-4'>
          {rules.length > 0 ? (
            rules.map((rule) => (
              <div
                key={rule.id}
                className='flex items-center justify-between rounded-lg border p-4'
              >
                <div>
                  <p className='font-semibold'>
                    {rule.rule_type === 'merchant_name' ? 'Merchant' : 'Keyword'}:{' '}
                    <span className='font-normal'>{rule.match_criteria}</span>
                  </p>
                  <p className='text-sm text-gray-500'>
                    Maps to: {getCategoryName(rule.category_id)}
                  </p>
                </div>
                <div className='flex gap-2'>
                  <Button variant='outline' size='sm' onClick={() => onEdit(rule)}>
                    Edit
                  </Button>
                  <Button variant='destructive' size='sm' onClick={() => onDelete(rule.id)}>
                    Delete
                  </Button>
                </div>
              </div>
            ))
          ) : (
            <p>You have no rules defined.</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default RuleList;
