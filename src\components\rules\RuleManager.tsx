'use client';

import { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import RuleForm, { Rule, RuleFormData } from './RuleForm';
import RuleList from './RuleList';

interface Category {
  id: string;
  name: string;
}

const RuleManager = () => {
  const [rules, setRules] = useState<Rule[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [editingRule, setEditingRule] = useState<Rule | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const fetchRules = useCallback(async () => {
    try {
      const response = await fetch('/api/rules/get');
      if (!response.ok) {
        throw new Error('Failed to fetch rules');
      }
      const data = await response.json();
      setRules(data.rules || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    }
  }, []);

  const fetchCategories = useCallback(async () => {
    try {
      const response = await fetch('/api/categories/get');
      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }
      const data = await response.json();
      setCategories(data.categories || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    }
  }, []);

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([fetchRules(), fetchCategories()]);
      setLoading(false);
    };
    loadData();
  }, [fetchRules, fetchCategories]);

  const handleSave = async (data: RuleFormData) => {
    setIsSubmitting(true);
    setError(null);
    try {
      const url = editingRule ? `/api/rules/update?id=${editingRule.id}` : '/api/rules/create';
      const method = editingRule ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw errorData;
      }

      await fetchRules();
      setIsFormVisible(false);
      setEditingRule(null);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (err: any) {
      // Use 'any' to handle the unknown error structure
      setError(err?.error || 'An unknown error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this rule?')) {
      return;
    }
    setError(null);
    try {
      const response = await fetch(`/api/rules/delete?id=${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete rule');
      }

      await fetchRules();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    }
  };

  const handleAddNewClick = () => {
    setEditingRule(null);
    setIsFormVisible(true);
  };

  const handleEditClick = (rule: Rule) => {
    setEditingRule(rule);
    setIsFormVisible(true);
  };

  const handleCancel = () => {
    setIsFormVisible(false);
    setEditingRule(null);
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <div className='space-y-6'>
      {error && <div className='text-red-500 bg-red-100 p-3 rounded-md'>Error: {error}</div>}

      {!isFormVisible && (
        <div className='flex justify-end'>
          <Button onClick={handleAddNewClick}>Add New Rule</Button>
        </div>
      )}

      {isFormVisible ? (
        <RuleForm
          editingRule={editingRule}
          isSubmitting={isSubmitting}
          onSave={handleSave}
          onCancel={handleCancel}
          categories={categories}
        />
      ) : (
        <RuleList
          rules={rules}
          categories={categories}
          onEdit={handleEditClick}
          onDelete={handleDelete}
        />
      )}
    </div>
  );
};

export default RuleManager;
