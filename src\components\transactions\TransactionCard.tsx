'use client';

import React from 'react';
import { toast } from 'sonner';
import { Card, CardContent } from '@/components/ui/card';
import CategorySelector from '@/components/categories/CategorySelector';

export interface Transaction {
  id: string;
  amount: number;
  currency_code: string;
  transaction_date: string;
  authorized_date: string | null;
  posted_date: string | null;
  merchant_name: string | null;
  description: string;
  category_id: string | null;
  user_category_id: string | null;
  plaid_category: string[] | null;
  plaid_category_detailed: string[] | null;
  transaction_type: string;
  location: Record<string, unknown> | null;
  is_pending: boolean;
  is_recurring: boolean;
  status: string;
  tags: string[] | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
  financial_accounts: {
    id: string;
    account_name: string;
    institution_name: string;
    account_type: string;
    account_subtype: string;
    mask: string | null;
  };
}

interface TransactionCardProps {
  transaction: Transaction;
  onClick: (transaction: Transaction) => void;
  isSelected: boolean;
  onToggleSelected: (transactionId: string) => void;
}

/**
 * TransactionCard component displays detailed information about a single transaction.
 * It includes transaction amount, date, merchant, and a CategorySelector dropdown
 * to assign or change the user category for the transaction.
 */
export default function TransactionCard({
  transaction,
  onClick,
  isSelected,
  onToggleSelected,
}: TransactionCardProps) {
  const [isUpdating, setIsUpdating] = React.useState(false);
  const [selectedCategoryId, setSelectedCategoryId] = React.useState(
    transaction.user_category_id || ''
  );

  const handleCreateRule = async (newCategoryId: string) => {
    try {
      const response = await fetch('/api/rules/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          field: 'merchant_name',
          operator: 'equals',
          value: transaction.merchant_name,
          categoryId: newCategoryId,
        }),
      });

      if (response.ok) {
        toast.success('Rule created successfully!');
      } else {
        const errorData = await response.json();
        toast.error(`Failed to create rule: ${errorData.message}`);
      }
    } catch (error) {
      console.error('An error occurred while creating the rule:', error);
      toast.error('An error occurred while creating the rule.');
    }
  };

  const handleCategoryChange = async (newCategoryId: string) => {
    setIsUpdating(true);
    try {
      const response = await fetch('/api/transactions/update-category', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactionId: transaction.id,
          newCategoryId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Failed to update category:', errorData.error);
        toast.error(`Failed to update category: ${errorData.error}`);
      } else {
        setSelectedCategoryId(newCategoryId);
        toast.success('Category updated.', {
          action: {
            label: 'Create Rule',
            onClick: () => handleCreateRule(newCategoryId),
          },
        });
      }
    } catch (error) {
      console.error('An error occurred while updating the category:', error);
      toast.error('An error occurred while updating the category.');
    } finally {
      setIsUpdating(false);
    }
  };

  const formatAmount = (amount: number, currencyCode: string) => {
    const isNegative = amount < 0;
    const absoluteAmount = Math.abs(amount);
    const formatted = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode || 'USD',
    }).format(absoluteAmount);

    return { formatted, isNegative };
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const { formatted: amountFormatted, isNegative } = formatAmount(
    transaction.amount,
    transaction.currency_code
  );

  const displayName = transaction.merchant_name || transaction.description;
  const accountInfo = transaction.financial_accounts;

  return (
    <Card
      className={`transition-shadow ${
        isSelected ? 'ring-2 ring-indigo-500 shadow-lg' : 'hover:shadow-md'
      }`}
      onClick={() => onClick(transaction)}
    >
      <CardContent className='p-4'>
        <div className='flex items-center justify-between'>
          {/* Left side - Checkbox and Transaction details */}
          <div className='flex flex-1 min-w-0 items-center space-x-4'>
            <input
              type='checkbox'
              className='h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500'
              checked={isSelected}
              onChange={(e) => {
                e.stopPropagation();
                onToggleSelected(transaction.id);
              }}
            />
            <div className='flex-1 min-w-0'>
              <div className='flex items-center space-x-3'>
                {/* Transaction icon/indicator */}
                <div
                  className={`w-3 h-3 rounded-full ${isNegative ? 'bg-red-500' : 'bg-green-500'}`}
                />

                {/* Main transaction info */}
                <div className='flex-1 min-w-0'>
                  <h3 className='text-sm font-medium text-gray-900 truncate'>{displayName}</h3>
                </div>
                <div className='flex items-center space-x-2 text-xs text-gray-500'>
                  <span>{accountInfo.account_name}</span>
                  <span>•</span>
                  <span>{accountInfo.institution_name}</span>
                  {accountInfo.mask && (
                    <>
                      <span>•</span>
                      <span>****{accountInfo.mask}</span>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Additional details */}
            <div className='mt-2 flex items-center space-x-4 text-xs text-gray-500'>
              <span>{formatDate(transaction.transaction_date)}</span>
              {transaction.is_pending && (
                <span className='px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full'>
                  Pending
                </span>
              )}
              {transaction.is_recurring && (
                <span className='px-2 py-1 bg-blue-100 text-blue-800 rounded-full'>Recurring</span>
              )}
              {transaction.plaid_category && transaction.plaid_category.length > 0 && (
                <span className='px-2 py-1 bg-gray-100 text-gray-700 rounded-full'>
                  {transaction.plaid_category[0]}
                </span>
              )}
            </div>
          </div>

          {/* Right side - Amount */}
          <div className='ml-4 flex-shrink-0'>
            <div
              className={`text-lg font-semibold ${isNegative ? 'text-red-600' : 'text-green-600'}`}
            >
              {isNegative ? '-' : '+'}
              {amountFormatted}
            </div>
            <div className='text-xs text-gray-500 text-right'>{transaction.transaction_type}</div>
          </div>
        </div>

        <CategorySelector
          selectedValue={selectedCategoryId}
          onValueChange={handleCategoryChange}
          disabled={isUpdating}
        />

        {/* Notes section if present */}
        {transaction.notes && (
          <div className='mt-3 pt-3 border-t border-gray-100'>
            <p className='text-sm text-gray-600'>{transaction.notes}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
