'use client';

import React from 'react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useCategories, CategoryFormData, Category } from '@/lib/hooks/useCategories';
import CategoryForm from './CategoryForm';
import CategoryList from './CategoryList';

/**
 * CategoryManager container component that orchestrates category management.
 * Uses the useCategories hook for state management and renders CategoryForm and CategoryList components.
 */
export default function CategoryManager() {
  const {
    categories,
    isLoading,
    isSubmitting,
    error,
    editingCategory,
    createCategory,
    updateCategory,
    deleteCategory,
    setEditingCategory,
  } = useCategories();

  const handleSubmit = async (data: CategoryFormData) => {
    if (editingCategory) {
      await updateCategory(editingCategory.id, data);
    } else {
      await createCategory(data);
    }
  };

  const handleCancel = () => {
    setEditingCategory(null);
  };

  const handleEdit = (category: Category) => {
    setEditingCategory(category);
  };

  if (isLoading) {
    return (
      <div className='flex justify-center items-center p-8'>
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Error Display */}
      {error && (
        <div className='bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded'>
          {error}
        </div>
      )}

      {/* Category Form */}
      <CategoryForm
        editingCategory={editingCategory}
        isSubmitting={isSubmitting}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
      />

      {/* Categories List */}
      <CategoryList categories={categories} onEdit={handleEdit} onDelete={deleteCategory} />
    </div>
  );
}
