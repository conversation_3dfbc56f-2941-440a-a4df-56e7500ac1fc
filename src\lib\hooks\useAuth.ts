import { useState } from 'react';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import { Credentials } from '@/components/auth/LoginForm';
import { AuthError } from '@supabase/supabase-js';

const supabase = createSupabaseBrowserClient();

export function useAuth() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<AuthError | null>(null);

  const signIn = async (credentials: Credentials) => {
    setIsLoading(true);
    setError(null);
    try {
      const { error } = await supabase.auth.signInWithPassword(credentials);
      if (error) {
        throw error;
      }
    } catch (error: unknown) {
      if (error instanceof AuthError) {
        setError(error);
      } else {
        setError(new AuthError('An unexpected error occurred.'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (credentials: Credentials) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await supabase.auth.signUp({
        email: credentials.email,
        password: credentials.password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/email-confirmed`,
        },
      });
      if (response.error) {
        throw response.error;
      }
      return response;
    } catch (error: unknown) {
      if (error instanceof AuthError) {
        setError(error);
      } else {
        setError(new AuthError('An unexpected error occurred.'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        throw error;
      }
    } catch (error: unknown) {
      if (error instanceof AuthError) {
        setError(error);
      } else {
        setError(new AuthError('An unexpected error occurred during logout.'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  return { signIn, signUp, logout, isLoading, error };
}
