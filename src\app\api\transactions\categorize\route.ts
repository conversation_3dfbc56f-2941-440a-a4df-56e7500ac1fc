import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { verifyOwnership } from '@/lib/utils';

export async function POST(request: NextRequest) {
  const supabase = await createSupabaseServerClient();

  // Check authentication
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const userId = user.id;

  // Parse request body
  let body;
  try {
    body = await request.json();
  } catch {
    return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
  }

  const { transaction_id, user_category_id } = body;

  if (!transaction_id || !user_category_id) {
    return NextResponse.json(
      { error: 'transaction_id and user_category_id are required' },
      { status: 400 }
    );
  }

  try {
    // Verify ownership of both the transaction and the category
    const isTransactionOwner = await verifyOwnership(
      supabase,
      userId,
      'transactions',
      transaction_id
    );
    if (!isTransactionOwner) {
      return NextResponse.json({ error: 'Transaction not found or unauthorized' }, { status: 404 });
    }

    const isCategoryOwner = await verifyOwnership(
      supabase,
      userId,
      'user_categories',
      user_category_id
    );
    if (!isCategoryOwner) {
      return NextResponse.json({ error: 'Category not found or unauthorized' }, { status: 404 });
    }

    // Update transaction with new user_category_id
    const { data: updatedTransaction, error: updateError } = await supabase
      .from('transactions')
      .update({ user_category_id })
      .eq('id', transaction_id)
      .single();

    if (updateError) {
      return NextResponse.json({ error: 'Failed to update transaction category' }, { status: 500 });
    }

    return NextResponse.json(updatedTransaction, { status: 200 });
  } catch (error) {
    console.error('[TRANSACTIONS_CATEGORIZE_ERROR]', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
