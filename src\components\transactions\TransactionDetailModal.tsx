import React from 'react';
import { Card, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Transaction } from '@/components/transactions/TransactionCard';

interface TransactionDetailModalProps {
  transaction: Transaction;
  onClose: () => void;
}

const TransactionDetailModal: React.FC<TransactionDetailModalProps> = ({
  transaction,
  onClose,
}) => {
  return (
    <div className='fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50'>
      <Card className='w-full max-w-lg'>
        <CardHeader>
          <CardTitle>Transaction Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            <div>
              <h3 className='font-semibold'>Description</h3>
              <p>{transaction.description}</p>
            </div>
            <div>
              <h3 className='font-semibold'>Amount</h3>
              <p>
                {new Intl.NumberFormat('en-US', {
                  style: 'currency',
                  currency: transaction.currency_code || 'USD',
                }).format(transaction.amount)}
              </p>
            </div>
            <div>
              <h3 className='font-semibold'>Date</h3>
              <p>{new Date(transaction.transaction_date).toLocaleDateString()}</p>
            </div>
            <div>
              <h3 className='font-semibold'>Category</h3>
              <p>
                {Array.isArray(transaction.plaid_category_detailed)
                  ? transaction.plaid_category_detailed.join(' > ')
                  : transaction.plaid_category_detailed}
              </p>
            </div>
            <div>
              <h3 className='font-semibold'>Account</h3>
              <p>
                {transaction.financial_accounts.account_name} (
                {transaction.financial_accounts.institution_name})
              </p>
            </div>
            <div>
              <h3 className='font-semibold'>Status</h3>
              <p>{transaction.status}</p>
            </div>
          </div>
          <div className='mt-6 flex justify-end'>
            <Button onClick={onClose}>Close</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TransactionDetailModal;
