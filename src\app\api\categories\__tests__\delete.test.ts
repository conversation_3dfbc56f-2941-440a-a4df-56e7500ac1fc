import { NextRequest, NextResponse } from 'next/server';

// Mock dependencies BEFORE importing the route
jest.mock('@/lib/supabase/server');
jest.mock('@/lib/services/categoryService', () => ({
  deleteCategory: jest.fn(),
  CategoryDeletionError: class CategoryDeletionError extends Error {
    status: number;
    details?: string;

    constructor(message: string, status: number, details?: string) {
      super(message);
      this.name = 'CategoryDeletionError';
      this.status = status;
      this.details = details;
    }
  },
}));

// Override the global NextResponse mock from jest.setup.js
const mockNextResponse = {
  json: jest.fn((body, init) => ({
    status: init?.status || 200,
    json: () => Promise.resolve(body),
  })),
};

jest.doMock('next/server', () => ({
  ...jest.requireActual('next/server'),
  NextResponse: mockNextResponse,
}));

// Import after mocking
import { DELETE } from '../delete/route';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { deleteCategory, CategoryDeletionError } from '@/lib/services/categoryService';

describe('DELETE /api/categories/delete', () => {
  const mockUser = { id: 'test-user-id' };
  const mockCategoryId = 'cat-123';
  const mockSupabase: any = {
    auth: {
      getUser: jest.fn(),
    },
  };

  const createMockRequest = (body: Record<string, unknown>) => {
    return new Request('http://localhost/api/categories/delete', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    }) as NextRequest;
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);
    // Reset the deleteCategory mock to default behavior
    (deleteCategory as jest.Mock).mockReset();
  });

  test('Success (200 OK) - deletes category successfully', async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: mockUser },
      error: null,
    });
    (deleteCategory as jest.Mock).mockResolvedValue({
      id: mockCategoryId,
      name: 'Test Category',
    });

    const req = createMockRequest({ category_id: mockCategoryId });
    const res = await DELETE(req);
    const body = await res.json();

    expect(res.status).toBe(200);
    expect(body).toEqual({
      message: 'Category deleted successfully',
      deleted_category: {
        id: mockCategoryId,
        name: 'Test Category',
      },
    });
    expect(deleteCategory).toHaveBeenCalledWith(mockSupabase, mockCategoryId, mockUser.id);
  });

  test('Unauthorized (401) - no user authenticated', async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: null },
      error: { message: 'Unauthorized' },
    });

    const req = createMockRequest({ category_id: mockCategoryId });
    const res = await DELETE(req);
    const body = await res.json();

    expect(res.status).toBe(401);
    expect(body).toEqual({ error: 'Unauthorized' });
    expect(deleteCategory).not.toHaveBeenCalled();
  });

  test('Bad Request (400) - missing category_id', async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: mockUser },
      error: null,
    });

    const req = createMockRequest({});
    const res = await DELETE(req);
    const body = await res.json();

    expect(res.status).toBe(400);
    expect(body).toEqual({
      error: 'Category ID is required and must be a string',
    });
    expect(deleteCategory).not.toHaveBeenCalled();
  });

  test('Category Not Found (404) - from service', async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: mockUser },
      error: null,
    });

    const mockError = new CategoryDeletionError('Category not found', 404);
    (deleteCategory as jest.Mock).mockRejectedValue(mockError);

    const req = createMockRequest({ category_id: 'cat-not-found' });
    const res = await DELETE(req);
    const body = await res.json();

    expect(deleteCategory).toHaveBeenCalledWith(mockSupabase, 'cat-not-found', mockUser.id);
    expect(res.status).toBe(404);
    expect(body).toEqual({ error: 'Category not found', details: undefined });
  });

  test('Category In Use (409 Conflict) - from service', async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: mockUser },
      error: null,
    });
    (deleteCategory as jest.Mock).mockRejectedValue(
      new CategoryDeletionError('Cannot delete category in use', 409, 'Assigned to 5 transactions')
    );

    const req = createMockRequest({ category_id: mockCategoryId });
    const res = await DELETE(req);
    const body = await res.json();

    expect(res.status).toBe(409);
    expect(body).toEqual({
      error: 'Cannot delete category in use',
      details: 'Assigned to 5 transactions',
    });
  });

  test('Internal Server Error (500) - from service', async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: mockUser },
      error: null,
    });
    (deleteCategory as jest.Mock).mockRejectedValue(
      new CategoryDeletionError('Database error', 500)
    );

    const req = createMockRequest({ category_id: mockCategoryId });
    const res = await DELETE(req);
    const body = await res.json();

    expect(res.status).toBe(500);
    expect(body).toEqual({ error: 'Database error', details: undefined });
  });

  test('Internal Server Error (500) - unexpected error', async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: mockUser },
      error: null,
    });
    (deleteCategory as jest.Mock).mockRejectedValue(new Error('Something unexpected happened'));

    const req = createMockRequest({ category_id: mockCategoryId });
    const res = await DELETE(req);
    const body = await res.json();

    expect(res.status).toBe(500);
    expect(body).toEqual({ error: 'Internal Server Error' });
  });
});
