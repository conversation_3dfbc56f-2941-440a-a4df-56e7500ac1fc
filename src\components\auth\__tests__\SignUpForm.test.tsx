import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import SignUpForm from '../SignUpForm';
import * as Auth from '@/lib/hooks/useAuth';

// Mock the useAuth hook
const mockSignUp = jest.fn();
jest.mock('@/lib/hooks/useAuth');
const useAuthMock = Auth.useAuth as jest.Mock;

describe('SignUpForm Integration Tests', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
    useAuthMock.mockReturnValue({
      signUp: mockSignUp,
      isLoading: false,
      error: null,
    });
  });

  describe('Successful Registration', () => {
    it('should show success message on successful registration', async () => {
      const user = userEvent.setup();
      mockSignUp.mockResolvedValue({}); // Simulate successful sign-up with no error

      render(<SignUpForm />);

      // Fill out the form
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'password123');

      // Submit the form
      await user.click(submitButton);

      // Wait for the async operation to complete
      await waitFor(() => {
        expect(mockSignUp).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
          confirmPassword: 'password123',
        });
      });

      // Assert that success message is shown
      await waitFor(() => {
        expect(screen.getByText(/account created successfully/i)).toBeInTheDocument();
      });
    });

    it('should show loading state during registration', async () => {
      useAuthMock.mockReturnValue({
        signUp: mockSignUp,
        isLoading: true,
        error: null,
      });

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /creating account/i });

      // Check that loading state is shown
      expect(submitButton).toBeInTheDocument();
      expect(submitButton).toBeDisabled();
      expect(emailInput).toBeDisabled();
      expect(passwordInput).toBeDisabled();
      expect(confirmPasswordInput).toBeDisabled();
    });

    it('should reset form fields after successful registration', async () => {
      const user = userEvent.setup();
      mockSignUp.mockResolvedValue({});

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'password123');
      await user.click(submitButton);

      // Wait for success and form reset
      await waitFor(() => {
        expect(screen.getByText(/account created successfully/i)).toBeInTheDocument();
      });

      // Check that form fields are cleared
      expect(emailInput).toHaveValue('');
      expect(passwordInput).toHaveValue('');
      expect(confirmPasswordInput).toHaveValue('');
    });
  });

  describe('Failed Registration', () => {
    it('should display error message on registration failure', async () => {
      const user = userEvent.setup();
      const errorMessage = 'User already registered';
      useAuthMock.mockReturnValue({
        signUp: mockSignUp,
        isLoading: false,
        error: { message: errorMessage },
      });
      mockSignUp.mockResolvedValue({ error: { message: errorMessage } });

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'password123');
      await user.click(submitButton);

      // Wait for error message to appear
      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
      });

      // Ensure success message is not shown
      expect(screen.queryByText(/account created successfully/i)).not.toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    it('should show validation error for invalid email format', async () => {
      const user = userEvent.setup();

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      await user.type(emailInput, 'invalid-email');
      await user.tab(); // blur to trigger validation

      await waitFor(() => {
        expect(screen.getByText(/invalid email address/i)).toBeInTheDocument();
      });
      expect(mockSignUp).not.toHaveBeenCalled();
    });

    it('should show validation error for short password', async () => {
      const user = userEvent.setup();

      render(<SignUpForm />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      await user.type(passwordInput, '123'); // Too short
      await user.tab();

      await waitFor(() => {
        expect(screen.getByText('Password must be at least 6 characters')).toBeInTheDocument();
      });
      expect(mockSignUp).not.toHaveBeenCalled();
    });

    it('should show validation error for password mismatch', async () => {
      const user = userEvent.setup();

      render(<SignUpForm />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);

      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'differentpassword');
      await user.tab();

      await waitFor(() => {
        expect(screen.getByText('Passwords do not match')).toBeInTheDocument();
      });
      expect(mockSignUp).not.toHaveBeenCalled();
    });

    it('should show validation errors for empty fields on submit', async () => {
      const user = userEvent.setup();
      render(<SignUpForm />);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      await user.click(submitButton);

      expect(await screen.findByText('Email is required')).toBeInTheDocument();
      expect(screen.getByText('Password must be at least 6 characters')).toBeInTheDocument();
      expect(screen.getByText('Please confirm your password')).toBeInTheDocument();
      expect(mockSignUp).not.toHaveBeenCalled();
    });
  });

  describe('Form Accessibility', () => {
    it('should have proper form structure and labels', () => {
      render(<SignUpForm />);

      // Check for proper form structure (form element exists)
      const formElement = screen.getByRole('button', { name: /sign up/i }).closest('form');
      expect(formElement).toBeInTheDocument();

      // Check for proper labels
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument();

      // Check for submit button
      expect(screen.getByRole('button', { name: /sign up/i })).toBeInTheDocument();
    });

    it('should have proper input types and autocomplete attributes', () => {
      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);

      // Check input types
      expect(emailInput).toHaveAttribute('type', 'email');
      expect(passwordInput).toHaveAttribute('type', 'password');
      expect(confirmPasswordInput).toHaveAttribute('type', 'password');

      // Check autocomplete attributes
      expect(emailInput).toHaveAttribute('autocomplete', 'email');
      expect(passwordInput).toHaveAttribute('autocomplete', 'new-password');
      expect(confirmPasswordInput).toHaveAttribute('autocomplete', 'new-password');
    });

    it('should have proper placeholder text', () => {
      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);

      expect(emailInput).toHaveAttribute('placeholder', '<EMAIL>');
      expect(passwordInput).toHaveAttribute('placeholder', 'Enter password');
      expect(confirmPasswordInput).toHaveAttribute('placeholder', 'Re-enter password');
    });

    it('should have proper heading structure', () => {
      render(<SignUpForm />);

      const heading = screen.getByRole('heading', { level: 2 });
      expect(heading).toBeInTheDocument();
      expect(heading).toHaveTextContent('Create your NAVsync.io account');
    });

    it('should disable all form elements during loading', async () => {
      useAuthMock.mockReturnValue({
        signUp: mockSignUp,
        isLoading: true,
        error: null,
      });

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /creating account/i });

      expect(emailInput).toBeDisabled();
      expect(passwordInput).toBeDisabled();
      expect(confirmPasswordInput).toBeDisabled();
      expect(submitButton).toBeDisabled();
    });

    it('should have proper success message styling and content', async () => {
      const user = userEvent.setup();
      mockSignUp.mockResolvedValue({});

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'password123');
      await user.click(submitButton);

      await waitFor(() => {
        const successMessage = screen.getByText(
          /Account created successfully! Please check your email to verify your account./i
        );
        expect(successMessage).toBeInTheDocument();
        expect(successMessage).toHaveClass('text-green-600');
      });
    });

    it('should have proper error message styling', async () => {
      const user = userEvent.setup();
      const errorMessage = 'Test error';
      useAuthMock.mockReturnValue({
        signUp: mockSignUp,
        isLoading: false,
        error: { message: errorMessage },
      });
      mockSignUp.mockResolvedValue({ error: { message: errorMessage } });

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'password123');
      await user.click(submitButton);

      await waitFor(() => {
        const errorMessageElement = screen.getByText(errorMessage);
        expect(errorMessageElement).toBeInTheDocument();
        expect(errorMessageElement).toHaveClass('text-red-600');
      });
    });
  });
});
