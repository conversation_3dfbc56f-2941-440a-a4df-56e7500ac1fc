# Security Policy

## Introduction

The NAVsync team takes security seriously. We appreciate your efforts to responsibly disclose your findings, and we will make every effort to acknowledge your contributions.

## Reporting a Vulnerability

To report a security vulnerability, please email us at `<EMAIL>` with a detailed description of the vulnerability and steps to reproduce it. We will acknowledge your email within 48 hours and will send you a more detailed response within 72 hours indicating the next steps in handling your report.

## Disclosure Policy

After a vulnerability report is received, we will:

- Confirm the vulnerability and determine its impact.
- Work on a fix for the vulnerability.
- Release a patch or new version of the software that addresses the vulnerability.
- Publicly announce the vulnerability and credit the reporter, unless they wish to remain anonymous.

We ask that you do not publicly disclose the vulnerability until we have had a chance to address it. We will coordinate with you on the public disclosure.

## Scope

The following parts of the project are in scope for our security vulnerability program:

- [Placeholder for in-scope items, e.g., The NAVsync web application]
- [Placeholder for in-scope items, e.g., The NAVsync API]

## Out of Scope

The following are out of scope:

- [Placeholder for out-of-scope items, e.g., Denial of service attacks]
- [Placeholder for out-of-scope items, e.g., Social engineering of our staff or contractors]

## Safe Harbor

We consider activities conducted consistently with this policy to constitute authorized conduct under the Computer Fraud and Abuse Act. To the extent your activities are inconsistent with certain restrictions in our Acceptable Use Policy, we waive those restrictions for the limited purpose of permitting security research under this policy. We will not bring a DMCA claim against you for circumventing the technological measures we have used to protect the applications in our bug bounty program's scope.
