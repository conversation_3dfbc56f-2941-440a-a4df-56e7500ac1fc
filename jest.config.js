import nextJest from 'next/jest.js';

/** @type {import('jest').Config} */
const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files in your test environment
  dir: './',
});

// Add any custom config to be passed to Jest
const config = {
  coverageProvider: 'v8',
  testEnvironment: 'jsdom',
  testEnvironmentOptions: {
    url: 'http://localhost:3000',
  },
  // Add more setup options before each test is run
  setupFiles: [],
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapper: {
    // Handle module aliases
    '^@/(.*)$': '<rootDir>/src/$1',
    '^components/(.*)$': '<rootDir>/src/components/$1',
    '^lib/(.*)$': '<rootDir>/src/lib/$1',
    '^app/(.*)$': '<rootDir>/src/app/$1',
  },
  testPathIgnorePatterns: ['<rootDir>/.next/', '<rootDir>/node_modules/'],
};

const jestConfig = createJestConfig(config);

// Custom transform for ESM modules
const customJestConfig = async (...args) => {
  const finalConfig = await jestConfig(...args);
  finalConfig.transformIgnorePatterns = [
    // All node_modules are ignored by default, so we need to un-ignore the ones that use ESM
    '/node_modules/(?!(@supabase|@testing-library|@hookform)/)',
    '^.+\\.module\\.(css|sass|scss)$',
  ];
  return finalConfig;
};

export default customJestConfig;
