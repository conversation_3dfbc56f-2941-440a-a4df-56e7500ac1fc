'use client';

import TransactionsList from '@/components/transactions/TransactionsList';
import Link from 'next/link';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

function TransactionsPage() {
  return (
    <ProtectedRoute>
      <div className='container mx-auto p-4'>
        <div className='mb-6'>
          <h1 className='text-3xl font-bold mb-2'>Transactions</h1>
          <p className='text-gray-600'>View and manage your imported transactions</p>
        </div>

        <TransactionsList />

        <div className='mt-6'>
          <Link href='/dashboard' className='text-blue-600 hover:underline'>
            ← Back to Dashboard
          </Link>
        </div>
      </div>
    </ProtectedRoute>
  );
}

export default TransactionsPage;
