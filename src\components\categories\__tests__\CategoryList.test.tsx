import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import CategoryList from '../CategoryList';
import { Category } from '@/lib/hooks/useCategories';

// Mock UI components
jest.mock('@/components/ui/card', () => ({
  __esModule: true,
  Card: ({ children, ...props }: any) => (
    <div data-testid='card' {...props}>
      {children}
    </div>
  ),
  CardContent: ({ children, ...props }: any) => (
    <div data-testid='card-content' {...props}>
      {children}
    </div>
  ),
  CardDescription: ({ children, ...props }: any) => (
    <div data-testid='card-description' {...props}>
      {children}
    </div>
  ),
  CardHeader: ({ children, ...props }: any) => (
    <div data-testid='card-header' {...props}>
      {children}
    </div>
  ),
  CardTitle: ({ children, ...props }: any) => (
    <div data-testid='card-title' {...props}>
      {children}
    </div>
  ),
}));

jest.mock('@/components/ui/button', () => ({
  __esModule: true,
  Button: ({ children, onClick, variant, size, ...props }: any) => (
    <button onClick={onClick} data-variant={variant} data-size={size} {...props}>
      {children}
    </button>
  ),
}));

describe('CategoryList Component', () => {
  const mockCategories: Category[] = [
    {
      id: 'cat-1',
      name: 'Food & Dining',
      description: 'Restaurant and food purchases',
      color: '#ef4444',
      icon: '🍔',
      is_active: true,
      created_at: '2025-01-01T00:00:00Z',
      updated_at: '2025-01-02T00:00:00Z',
    },
    {
      id: 'cat-2',
      name: 'Transportation',
      description: 'Gas and car expenses',
      color: '#3b82f6',
      icon: '🚗',
      is_active: true,
      created_at: '2025-01-03T00:00:00Z',
      updated_at: '2025-01-04T00:00:00Z',
    },
  ];

  const defaultProps = {
    categories: mockCategories,
    onEdit: jest.fn(),
    onDelete: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    test('renders list header correctly', () => {
      render(<CategoryList {...defaultProps} />);

      expect(screen.getByText('Your Categories')).toBeInTheDocument();
      expect(screen.getByText('Manage your custom spending categories')).toBeInTheDocument();
    });

    test('renders all categories when categories exist', () => {
      render(<CategoryList {...defaultProps} />);

      expect(screen.getByText('Food & Dining')).toBeInTheDocument();
      expect(screen.getByText('Restaurant and food purchases')).toBeInTheDocument();
      expect(screen.getByText('Transportation')).toBeInTheDocument();
      expect(screen.getByText('Gas and car expenses')).toBeInTheDocument();
    });

    test('renders empty state when no categories exist', () => {
      render(<CategoryList {...defaultProps} categories={[]} />);

      expect(
        screen.getByText('No categories created yet. Create your first category above!')
      ).toBeInTheDocument();
    });
  });

  describe('Interactions', () => {
    test('calls onEdit when edit button is clicked', () => {
      const mockOnEdit = jest.fn();
      render(<CategoryList {...defaultProps} onEdit={mockOnEdit} />);

      const editButtons = screen.getAllByText('Edit');
      fireEvent.click(editButtons[0]);

      expect(mockOnEdit).toHaveBeenCalledWith(mockCategories[0]);
    });

    test('calls onDelete when delete button is clicked', () => {
      const mockOnDelete = jest.fn();
      render(<CategoryList {...defaultProps} onDelete={mockOnDelete} />);

      const deleteButtons = screen.getAllByText('Delete');
      fireEvent.click(deleteButtons[0]);

      expect(mockOnDelete).toHaveBeenCalledWith(mockCategories[0]);
    });

    test('renders correct number of edit and delete buttons', () => {
      render(<CategoryList {...defaultProps} />);

      const editButtons = screen.getAllByText('Edit');
      const deleteButtons = screen.getAllByText('Delete');

      expect(editButtons).toHaveLength(mockCategories.length);
      expect(deleteButtons).toHaveLength(mockCategories.length);
    });
  });

  describe('Category Display', () => {
    test('displays category icons and colors', () => {
      render(<CategoryList {...defaultProps} />);

      // Check that icons are displayed
      expect(screen.getByText('🍔')).toBeInTheDocument();
      expect(screen.getByText('🚗')).toBeInTheDocument();
    });

    test('handles categories without descriptions', () => {
      const categoriesWithoutDescription = [
        {
          ...mockCategories[0],
          description: null,
        },
      ];

      render(<CategoryList {...defaultProps} categories={categoriesWithoutDescription} />);

      expect(screen.getByText('Food & Dining')).toBeInTheDocument();
      expect(screen.queryByText('Restaurant and food purchases')).not.toBeInTheDocument();
    });
  });
});
