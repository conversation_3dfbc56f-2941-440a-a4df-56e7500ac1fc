import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CategoryForm from '../CategoryForm';
import { Category } from '@/lib/hooks/useCategories';

// Mock react-hook-form
const mockReset = jest.fn();
const mockHandleSubmit = jest.fn((fn: (data: any) => void) => (e: React.FormEvent) => {
  e.preventDefault();
  const formData = {
    name: 'Test Category',
    description: 'Test Description',
    color: '#ef4444',
    icon: '🍔',
  };
  fn(formData);
});

jest.mock('react-hook-form', () => ({
  useForm: () => ({
    control: {},
    handleSubmit: mockHandleSubmit,
    reset: mockReset,
    formState: { errors: {} },
    getValues: jest.fn(),
    setValue: jest.fn(),
    watch: jest.fn(),
  }),
  FormProvider: ({ children, ...props }: { children: React.ReactNode; [key: string]: any }) => {
    // Filter out react-hook-form methods that shouldn't be passed to DOM
    const { handleSubmit, reset, formState, getValues, setValue, watch, control, ...cleanProps } =
      props;
    return <div {...cleanProps}>{children}</div>;
  },
  Controller: ({ render }: { render: any }) =>
    render({ field: { onChange: jest.fn(), value: '' } }),
}));

// Mock the Form components
jest.mock('@/components/ui/form', () => ({
  Form: ({ children, ...props }: { children: React.ReactNode; [key: string]: any }) => {
    // Filter out react-hook-form methods that shouldn't be passed to DOM
    const { handleSubmit, reset, formState, getValues, setValue, watch, control, ...cleanProps } =
      props;
    return <div {...cleanProps}>{children}</div>;
  },
  FormField: ({ render }: { render: any }) => render({ field: { onChange: jest.fn(), value: '' } }),
  FormItem: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  FormLabel: ({ children }: { children: React.ReactNode }) => <label>{children}</label>,
  FormControl: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  FormMessage: () => <div />,
}));

// Mock UI components
jest.mock('@/components/ui/LoadingSpinner', () => ({
  __esModule: true,
  LoadingSpinner: () => <div data-testid='loading-spinner'>Loading...</div>,
}));

jest.mock('@/components/ui/button', () => ({
  __esModule: true,
  Button: ({ children, onClick, disabled, type, variant, ...props }: any) => (
    <button onClick={onClick} disabled={disabled} type={type} data-variant={variant} {...props}>
      {children}
    </button>
  ),
}));

jest.mock('@/components/ui/input', () => ({
  __esModule: true,
  Input: React.forwardRef<HTMLInputElement, any>((props, ref) => <input ref={ref} {...props} />),
}));

jest.mock('@/components/ui/card', () => ({
  __esModule: true,
  Card: ({ children, ...props }: any) => (
    <div data-testid='card' {...props}>
      {children}
    </div>
  ),
  CardContent: ({ children, ...props }: any) => (
    <div data-testid='card-content' {...props}>
      {children}
    </div>
  ),
  CardDescription: ({ children, ...props }: any) => (
    <div data-testid='card-description' {...props}>
      {children}
    </div>
  ),
  CardHeader: ({ children, ...props }: any) => (
    <div data-testid='card-header' {...props}>
      {children}
    </div>
  ),
  CardTitle: ({ children, ...props }: any) => (
    <div data-testid='card-title' {...props}>
      {children}
    </div>
  ),
}));

jest.mock('@/components/ui/form', () => ({
  __esModule: true,
  Form: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  FormControl: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  FormField: ({ render, name }: any) => {
    const field = {
      value: '',
      onChange: jest.fn(),
      onBlur: jest.fn(),
      name,
    };
    return render({ field });
  },
  FormItem: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  FormLabel: ({ children, ...props }: any) => <label {...props}>{children}</label>,
  FormMessage: ({ children, ...props }: any) => <div {...props}>{children}</div>,
}));

describe('CategoryForm Component', () => {
  const mockCategory: Category = {
    id: 'cat-1',
    name: 'Food & Dining',
    description: 'Restaurant and food purchases',
    color: '#ef4444',
    icon: '🍔',
    is_active: true,
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-02T00:00:00Z',
  };

  const defaultProps = {
    editingCategory: null,
    isSubmitting: false,
    onSubmit: jest.fn(),
    onCancel: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Create Mode', () => {
    test('renders create form when editingCategory is null', () => {
      render(<CategoryForm {...defaultProps} />);

      expect(screen.getByText('Create New Category')).toBeInTheDocument();
      expect(screen.getByText('Add a new custom spending category')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /create category/i })).toBeInTheDocument();
    });

    test('calls onSubmit with form data when create button is clicked', async () => {
      const mockOnSubmit = jest.fn().mockResolvedValue(undefined);
      render(<CategoryForm {...defaultProps} onSubmit={mockOnSubmit} />);

      const createButton = screen.getByRole('button', { name: /create category/i });
      fireEvent.click(createButton);

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith({
          name: 'Test Category',
          description: 'Test Description',
          color: '#ef4444',
          icon: '🍔',
        });
      });
    });
  });

  describe('Edit Mode', () => {
    test('renders edit form when editingCategory is provided', () => {
      render(<CategoryForm {...defaultProps} editingCategory={mockCategory} />);

      expect(screen.getByText('Edit Category')).toBeInTheDocument();
      expect(screen.getByText('Update the details of your category')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /update category/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
    });

    test('calls onSubmit with form data when update button is clicked', async () => {
      const mockOnSubmit = jest.fn().mockResolvedValue(undefined);
      render(
        <CategoryForm {...defaultProps} editingCategory={mockCategory} onSubmit={mockOnSubmit} />
      );

      const updateButton = screen.getByRole('button', { name: /update category/i });
      fireEvent.click(updateButton);

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith({
          name: 'Test Category',
          description: 'Test Description',
          color: '#ef4444',
          icon: '🍔',
        });
      });
    });

    test('calls onCancel when cancel button is clicked', () => {
      const mockOnCancel = jest.fn();
      render(
        <CategoryForm {...defaultProps} editingCategory={mockCategory} onCancel={mockOnCancel} />
      );

      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      fireEvent.click(cancelButton);

      expect(mockOnCancel).toHaveBeenCalled();
    });
  });

  describe('Submitting State', () => {
    test('shows loading state when isSubmitting is true in create mode', () => {
      render(<CategoryForm {...defaultProps} isSubmitting={true} />);

      expect(screen.getByText('Creating...')).toBeInTheDocument();
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });

    test('shows loading state when isSubmitting is true in edit mode', () => {
      render(<CategoryForm {...defaultProps} editingCategory={mockCategory} isSubmitting={true} />);

      expect(screen.getByText('Updating...')).toBeInTheDocument();
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });

    test('disables submit button when isSubmitting is true', () => {
      render(<CategoryForm {...defaultProps} isSubmitting={true} />);

      const submitButton = screen.getByRole('button', { name: /creating.../i });
      expect(submitButton).toBeDisabled();
    });
  });

  describe('Form Fields', () => {
    test('renders all form fields', () => {
      render(<CategoryForm {...defaultProps} />);

      expect(screen.getByText('Name')).toBeInTheDocument();
      expect(screen.getByText('Description (Optional)')).toBeInTheDocument();
      expect(screen.getByText('Color')).toBeInTheDocument();
      expect(screen.getByText('Icon')).toBeInTheDocument();
    });
  });
});
