'use client';

import React, { useEffect } from 'react';
import { useAuth } from './AuthProvider';
import { useProfile } from '@/lib/hooks/useProfile';
import { useAuth as useAuthHook } from '@/lib/hooks/useAuth';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';

const fullNameSchema = z.object({
  full_name: z
    .string()
    .min(2, 'Full name must be at least 2 characters')
    .max(50, 'Full name must be at most 50 characters')
    .optional()
    .or(z.literal('')), // Allows empty string to clear the name
});

type FullNameForm = z.infer<typeof fullNameSchema>;

export default function UserProfile() {
  const { user } = useAuth(); // user object from Supabase already has email
  const router = useRouter();

  // Use the new hooks
  const {
    profile,
    isLoading: profileLoading,
    error: profileError,
    updateProfile,
    updateLoading,
    updateError,
    updateSuccess,
  } = useProfile(user?.id);

  const { logout, isLoading: logoutLoading, error: logoutError } = useAuthHook();

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
  } = useForm<FullNameForm>({
    resolver: zodResolver(fullNameSchema),
    defaultValues: {
      full_name: '', // Initialize empty, will be set by useEffect
    },
  });

  // Keep form in sync with profile.full_name
  useEffect(() => {
    if (profile) {
      reset({ full_name: profile.full_name ?? '' });
    } else {
      reset({ full_name: '' }); // Reset to empty if profile is null
    }
  }, [profile, reset]);

  const onSubmitFullName = async (values: FullNameForm) => {
    await updateProfile(values.full_name || '');
    if (!updateError) {
      reset({ full_name: values.full_name || '' }); // Reset form to new values and clear dirty state
    }
  };

  const handleLogout = async () => {
    await logout();
    if (!logoutError) {
      router.push('/login'); // Redirect after successful logout
    }
  };

  if (!user) {
    // Should be handled by ProtectedRoute, but good for robustness
    return <p>Loading user...</p>; // Or redirect, though ProtectedRoute should handle this
  }

  return (
    <div className='flex justify-center items-center min-h-[60vh] px-2'>
      <Card className='w-full max-w-md shadow-lg'>
        <CardHeader>
          <CardTitle>User Profile</CardTitle>
        </CardHeader>
        <CardContent>
          {profileLoading ? (
            <div className='text-center text-muted-foreground py-4'>Loading profile...</div>
          ) : profileError ? (
            <div className='text-center text-destructive py-4' role='alert'>
              {profileError}
            </div>
          ) : (
            <div className='space-y-4'>
              <div>
                <Label className='block mb-1'>Email</Label>
                <div className='bg-muted rounded px-3 py-2 text-sm'>
                  {user.email} {/* Use email from useAuth().user directly */}
                </div>
              </div>
              <form
                className='space-y-2'
                onSubmit={handleSubmit(onSubmitFullName)}
                autoComplete='off'
              >
                <div>
                  {' '}
                  {/* Added div for better structure with label and input */}
                  <Label htmlFor='full_name' className='block mb-1'>
                    {' '}
                    {/* Added mb-1 for spacing */}
                    Full Name
                  </Label>
                  <Input
                    id='full_name'
                    {...register('full_name')}
                    placeholder='Enter your full name'
                    disabled={updateLoading}
                    className={errors.full_name ? 'border-destructive' : ''}
                  />
                  {errors.full_name && (
                    <div className='text-destructive text-xs mt-1'>
                      {' '}
                      {/* Added mt-1 for spacing */}
                      {errors.full_name.message}
                    </div>
                  )}
                </div>
                <Button
                  type='submit'
                  className='mt-2 w-full' // Ensure this class applies as intended
                  disabled={updateLoading || !isDirty}
                >
                  {updateLoading ? 'Updating...' : 'Update Full Name'}
                </Button>
                {updateError && (
                  <div className='text-destructive text-xs mt-1' role='alert'>
                    {' '}
                    {/* Added role="alert" */}
                    {updateError}
                  </div>
                )}
                {updateSuccess && (
                  <div className='text-green-600 text-xs mt-1'>
                    {' '}
                    {/* Changed from text-success to text-green-600 */}
                    {updateSuccess}
                  </div>
                )}
              </form>
            </div>
          )}
        </CardContent>
        <CardFooter className='flex flex-col gap-2 pt-4'>
          {' '}
          {/* Added pt-4 for spacing */}
          <Button
            variant='outline'
            className='w-full'
            onClick={handleLogout}
            disabled={updateLoading || logoutLoading} // Consider if logout should be disabled during profile update
          >
            {logoutLoading ? 'Logging out...' : 'Logout'}
          </Button>
          {logoutError && (
            <div className='text-destructive text-xs mt-1' role='alert'>
              {logoutError.message}
            </div>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
