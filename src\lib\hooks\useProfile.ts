'use client';

import { useState, useEffect, useCallback } from 'react';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';

const supabase = createSupabaseBrowserClient();

export type Profile = {
  id: string;
  full_name: string | null;
  onboarding_completed: boolean;
};

export type UseProfileReturn = {
  profile: Profile | null;
  isLoading: boolean;
  error: string | null;
  updateProfile: (fullName: string) => Promise<void>;
  updateLoading: boolean;
  updateError: string | null;
  updateSuccess: string | null;
  refetch: () => Promise<void>;
};

export function useProfile(userId: string | undefined): UseProfileReturn {
  const [profile, setProfile] = useState<Profile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [updateLoading, setUpdateLoading] = useState(false);
  const [updateError, setUpdateError] = useState<string | null>(null);
  const [updateSuccess, setUpdateSuccess] = useState<string | null>(null);

  const fetchProfile = useCallback(async () => {
    if (!userId) {
      setProfile(null);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, onboarding_completed')
        .eq('id', userId)
        .single();

      if (error) {
        // Handle case where profile doesn't exist yet
        if (error.code === 'PGRST116') {
          // PGRST116: "Searched for a single row, but found no rows"
          setProfile({ id: userId, full_name: null, onboarding_completed: false });
          console.warn('No profile record found for user, allowing creation/update.');
        } else {
          setError(`Failed to load profile: ${error.message}`);
          setProfile(null);
        }
      } else if (data) {
        setProfile(data);
      } else {
        setProfile({ id: userId, full_name: null, onboarding_completed: false });
      }
    } catch (e: unknown) {
      const errorMessage =
        e instanceof Error ? e.message : 'An unexpected error occurred while fetching profile.';
      setError(errorMessage);
      setProfile(null);
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  // Fetch profile data on initial load
  useEffect(() => {
    fetchProfile();
  }, [fetchProfile]);

  const updateProfile = async (fullName: string) => {
    if (!userId) return;

    setUpdateLoading(true);
    setUpdateError(null);
    setUpdateSuccess(null);

    try {
      const { error } = await supabase
        .from('profiles')
        .update({ full_name: fullName || null })
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        setUpdateError(`Failed to update full name: ${error.message}`);
      } else {
        setUpdateSuccess('Full name updated successfully.');
        // Optimistically update local profile state
        setProfile((prev) =>
          prev
            ? { ...prev, full_name: fullName || null }
            : { id: userId, full_name: fullName || null, onboarding_completed: false }
        );
      }
    } catch (e: unknown) {
      const errorMessage =
        e instanceof Error ? e.message : 'An unexpected error occurred during update.';
      setUpdateError(errorMessage);
    } finally {
      setUpdateLoading(false);
    }
  };

  return {
    profile,
    isLoading,
    error,
    updateProfile,
    updateLoading,
    updateError,
    updateSuccess,
    refetch: fetchProfile,
  };
}
