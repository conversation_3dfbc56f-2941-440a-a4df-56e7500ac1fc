import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import CategoryListItem from '../CategoryListItem';
import { Category } from '@/lib/hooks/useCategories';

// Mock UI components
jest.mock('@/components/ui/button', () => ({
  __esModule: true,
  Button: ({ children, onClick, variant, size, ...props }: any) => (
    <button onClick={onClick} data-variant={variant} data-size={size} {...props}>
      {children}
    </button>
  ),
}));

describe('CategoryListItem Component', () => {
  const mockCategory: Category = {
    id: 'cat-1',
    name: 'Food & Dining',
    description: 'Restaurant and food purchases',
    color: '#ef4444',
    icon: '🍔',
    is_active: true,
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-02T00:00:00Z',
  };

  const defaultProps = {
    category: mockCategory,
    onEdit: jest.fn(),
    onDelete: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    test('renders category information correctly', () => {
      render(<CategoryListItem {...defaultProps} />);

      expect(screen.getByText('Food & Dining')).toBeInTheDocument();
      expect(screen.getByText('Restaurant and food purchases')).toBeInTheDocument();
      expect(screen.getByText('🍔')).toBeInTheDocument();
    });

    test('renders category without description', () => {
      const categoryWithoutDescription = {
        ...mockCategory,
        description: null,
      };

      render(<CategoryListItem {...defaultProps} category={categoryWithoutDescription} />);

      expect(screen.getByText('Food & Dining')).toBeInTheDocument();
      expect(screen.queryByText('Restaurant and food purchases')).not.toBeInTheDocument();
    });

    test('renders default icon when icon is null', () => {
      const categoryWithoutIcon = {
        ...mockCategory,
        icon: null,
      };

      render(<CategoryListItem {...defaultProps} category={categoryWithoutIcon} />);

      expect(screen.getByText('📁')).toBeInTheDocument();
    });

    test('renders default color when color is null', () => {
      const categoryWithoutColor = {
        ...mockCategory,
        color: null,
      };

      const { container } = render(
        <CategoryListItem {...defaultProps} category={categoryWithoutColor} />
      );

      const iconElement = container.querySelector(
        '[style*="background-color: rgb(107, 114, 128)"]'
      );
      expect(iconElement).toBeInTheDocument();
    });
  });

  describe('Interactions', () => {
    test('calls onEdit when edit button is clicked', () => {
      const mockOnEdit = jest.fn();
      render(<CategoryListItem {...defaultProps} onEdit={mockOnEdit} />);

      const editButton = screen.getByText('Edit');
      fireEvent.click(editButton);

      expect(mockOnEdit).toHaveBeenCalledWith(mockCategory);
    });

    test('calls onDelete when delete button is clicked', () => {
      const mockOnDelete = jest.fn();
      render(<CategoryListItem {...defaultProps} onDelete={mockOnDelete} />);

      const deleteButton = screen.getByText('Delete');
      fireEvent.click(deleteButton);

      expect(mockOnDelete).toHaveBeenCalledWith(mockCategory);
    });

    test('renders edit and delete buttons with correct variants', () => {
      render(<CategoryListItem {...defaultProps} />);

      const editButton = screen.getByText('Edit');
      const deleteButton = screen.getByText('Delete');

      expect(editButton).toHaveAttribute('data-variant', 'outline');
      expect(editButton).toHaveAttribute('data-size', 'sm');
      expect(deleteButton).toHaveAttribute('data-variant', 'destructive');
      expect(deleteButton).toHaveAttribute('data-size', 'sm');
    });
  });

  describe('Styling', () => {
    test('applies correct CSS classes', () => {
      const { container } = render(<CategoryListItem {...defaultProps} />);

      const itemContainer = container.firstChild;
      expect(itemContainer).toHaveClass(
        'flex',
        'items-center',
        'justify-between',
        'p-4',
        'border',
        'rounded-lg',
        'hover:bg-gray-50'
      );
    });

    test('applies custom background color to icon', () => {
      const { container } = render(<CategoryListItem {...defaultProps} />);

      const iconElement = container.querySelector('[style*="background-color: rgb(239, 68, 68)"]');
      expect(iconElement).toBeInTheDocument();
    });
  });
});
