'use client';

import CategoryManager from '@/components/categories/CategoryManager';
import Link from 'next/link';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

function CategoriesPage() {
  return (
    <ProtectedRoute>
      <div className='container mx-auto p-4'>
        <div className='mb-6'>
          <h1 className='text-3xl font-bold mb-2'>Categories</h1>
          <p className='text-gray-600'>Create and manage your custom spending categories</p>
        </div>

        <CategoryManager />

        <div className='mt-6'>
          <Link href='/dashboard' className='text-blue-600 hover:underline'>
            ← Back to Dashboard
          </Link>
        </div>
      </div>
    </ProtectedRoute>
  );
}

export default CategoriesPage;
