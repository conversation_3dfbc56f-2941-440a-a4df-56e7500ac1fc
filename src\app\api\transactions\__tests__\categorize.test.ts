import { POST } from '../categorize/route';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest } from 'next/server';
import { verifyOwnership } from '@/lib/utils';

// Mock dependencies
jest.mock('@/lib/supabase/server', () => ({
  createSupabaseServerClient: jest.fn(),
}));

jest.mock('@/lib/utils', () => ({
  verifyOwnership: jest.fn(),
}));

jest.mock('next/server', () => ({
  ...jest.requireActual('next/server'),
  NextResponse: {
    json: jest.fn((body, init) => ({
      status: init?.status || 200,
      json: () => Promise.resolve(body),
    })),
  },
}));

describe('POST /api/transactions/categorize', () => {
  const mockUser = { id: 'test-user-id' };
  const mockTransactionId = 'txn-123';
  const mockCategoryId = 'cat-456';

  const createMockRequest = (body: { transaction_id: string; user_category_id: string }) => {
    return new Request('http://localhost/api/transactions/categorize', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    }) as NextRequest;
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('Success (200 OK)', async () => {
    const mockUpdatedTransaction = {
      id: mockTransactionId,
      user_id: mockUser.id,
      user_category_id: mockCategoryId,
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockImplementation((table: string) => {
        if (table === 'transactions') {
          return {
            update: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({ data: mockUpdatedTransaction, error: null }),
              }),
            }),
          };
        }
        return {};
      }),
    };
    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);
    (verifyOwnership as jest.Mock).mockResolvedValue(true);

    const req = createMockRequest({
      transaction_id: mockTransactionId,
      user_category_id: mockCategoryId,
    });
    const res = await POST(req);
    const body = await res.json();

    expect(res.status).toBe(200);
    expect(body).toEqual(mockUpdatedTransaction);
    expect(verifyOwnership).toHaveBeenCalledWith(
      expect.anything(),
      mockUser.id,
      'transactions',
      mockTransactionId
    );
    expect(verifyOwnership).toHaveBeenCalledWith(
      expect.anything(),
      mockUser.id,
      'user_categories',
      mockCategoryId
    );
  });

  test('Unauthorized (401)', async () => {
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest
          .fn()
          .mockResolvedValue({ data: { user: null }, error: { message: 'Unauthorized' } }),
      },
    });

    const req = createMockRequest({
      transaction_id: mockTransactionId,
      user_category_id: mockCategoryId,
    });
    const res = await POST(req);

    expect(res.status).toBe(401);
    expect(await res.json()).toEqual({ error: 'Unauthorized' });
  });

  test('Transaction Not Found or Unauthorized (404)', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    };
    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);
    (verifyOwnership as jest.Mock).mockImplementation(
      async (supabase, userId, tableName, recordId) => {
        if (tableName === 'transactions') {
          return false;
        }
        return true;
      }
    );

    const req = createMockRequest({
      transaction_id: mockTransactionId,
      user_category_id: mockCategoryId,
    });
    const res = await POST(req);

    expect(res.status).toBe(404);
    expect(await res.json()).toEqual({ error: 'Transaction not found or unauthorized' });
  });

  test('Category Not Found or Unauthorized (404)', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    };
    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);
    (verifyOwnership as jest.Mock).mockImplementation(
      async (supabase, userId, tableName, recordId) => {
        if (tableName === 'user_categories') {
          return false;
        }
        return true;
      }
    );

    const req = createMockRequest({
      transaction_id: mockTransactionId,
      user_category_id: mockCategoryId,
    });
    const res = await POST(req);

    expect(res.status).toBe(404);
    expect(await res.json()).toEqual({ error: 'Category not found or unauthorized' });
  });

  test('Database Update Error (500)', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockImplementation((table: string) => {
        if (table === 'transactions') {
          return {
            update: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                single: jest
                  .fn()
                  .mockResolvedValue({ data: null, error: { message: 'Update failed' } }),
              }),
            }),
          };
        }
        return {};
      }),
    };
    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);
    (verifyOwnership as jest.Mock).mockResolvedValue(true);

    const req = createMockRequest({
      transaction_id: mockTransactionId,
      user_category_id: mockCategoryId,
    });
    const res = await POST(req);

    expect(res.status).toBe(500);
    expect(await res.json()).toEqual({ error: 'Failed to update transaction category' });
  });
});
