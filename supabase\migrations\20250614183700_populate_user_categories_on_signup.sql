-- Migration: Populate user categories on signup
-- This migration creates a function and trigger to automatically populate
-- user_categories with system categories when a new user signs up

-- Create function to populate user categories for new users
CREATE OR REPLACE FUNCTION populate_user_categories()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert all system categories into user_categories for the new user
    INSERT INTO user_categories (
        user_id,
        name,
        description,
        base_category_id,
        icon,
        color,
        is_active
    )
    SELECT 
        NEW.id,                    -- user_id from the newly created user
        tc.name,                   -- category name
        tc.description,            -- category description
        tc.id,                     -- reference to the original system category
        tc.icon,                   -- category icon
        tc.color,                  -- category color
        tc.is_active               -- active status
    FROM transaction_categories tc
    WHERE tc.category_type = 'system'
    AND tc.is_active = TRUE;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to execute the function after user insertion
CREATE TRIGGER trigger_populate_user_categories
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION populate_user_categories();

-- Add comment for documentation
COMMENT ON FUNCTION populate_user_categories() IS 'Automatically populates user_categories with system categories when a new user signs up';