import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import PlaidLink from '../PlaidLink';
import { usePlaidLink } from '@/lib/hooks/usePlaidLink';

// Mock the usePlaidLink hook
jest.mock('@/lib/hooks/usePlaidLink');

const mockUsePlaidLink = usePlaidLink as jest.Mock;

describe('PlaidLink Component', () => {
  const mockOpen = jest.fn();
  const mockOnSuccess = jest.fn();
  const mockOnExit = jest.fn();
  const userId = 'test-user-id';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Test Case 1: Initial Rendering and Button Click
  test('renders the connect button and calls open on click', async () => {
    mockUsePlaidLink.mockReturnValue({
      open: mockOpen,
      ready: true,
      isLoading: false,
      linkToken: 'mock-link-token',
    });

    render(<PlaidLink userId={userId} onSuccess={mockOnSuccess} onExit={mockOnExit} />);

    const connectButton = screen.getByRole('button', { name: /connect a bank account/i });
    expect(connectButton).toBeInTheDocument();
    expect(connectButton).not.toBeDisabled();

    fireEvent.click(connectButton);

    expect(mockOpen).toHaveBeenCalledTimes(1);
  });

  // Test Case 2: Button is disabled when loading
  test('button is disabled when loading', () => {
    mockUsePlaidLink.mockReturnValue({
      open: mockOpen,
      ready: false,
      isLoading: true,
      linkToken: null,
    });

    render(<PlaidLink userId={userId} onSuccess={mockOnSuccess} onExit={mockOnExit} />);

    const connectButton = screen.getByRole('button', { name: /loading/i });
    expect(connectButton).toBeDisabled();
  });

  // Test Case 3: Button is disabled when not ready
  test('button is disabled when not ready', () => {
    mockUsePlaidLink.mockReturnValue({
      open: mockOpen,
      ready: false,
      isLoading: false,
      linkToken: 'mock-link-token',
    });

    render(<PlaidLink userId={userId} onSuccess={mockOnSuccess} onExit={mockOnExit} />);

    const connectButton = screen.getByRole('button', { name: /connect a bank account/i });
    expect(connectButton).toBeDisabled();
  });

  // Test Case 4: usePlaidLink is called with correct props
  test('usePlaidLink is called with correct props', () => {
    mockUsePlaidLink.mockReturnValue({
      open: mockOpen,
      ready: true,
      isLoading: false,
      linkToken: 'mock-link-token',
    });

    render(<PlaidLink userId={userId} onSuccess={mockOnSuccess} onExit={mockOnExit} />);

    expect(mockUsePlaidLink).toHaveBeenCalledWith({
      userId,
      onSuccess: mockOnSuccess,
      onExit: mockOnExit,
    });
  });
});
