import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useRouter } from 'next/navigation';
import LoginForm from '../LoginForm';
import { useAuth } from '@/lib/hooks/useAuth';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

// Mock the useAuth hook
jest.mock('@/lib/hooks/useAuth');

const mockSignIn = jest.fn();
const mockUseAuth = useAuth as jest.Mock;

const mockPush = jest.fn();
const mockedUseRouter = useRouter as jest.Mock;

describe('LoginForm Integration Tests', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();

    // Setup router mock
    mockedUseRouter.mockReturnValue({
      push: mockPush,
    });
  });

  describe('Successful Login', () => {
    it('should redirect to dashboard on successful login', async () => {
      const user = userEvent.setup();

      // Mock successful authentication
      mockUseAuth.mockReturnValue({
        signIn: mockSignIn.mockResolvedValue({ error: null }),
        isLoading: false,
        error: null,
      });

      render(<LoginForm />);

      // Fill out the form
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');

      // Submit the form
      await user.click(submitButton);

      // Wait for the async operation to complete
      await waitFor(() => {
        expect(mockSignIn).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
        });
      });

      // Assert that router.push was called with the correct path
      expect(mockPush).toHaveBeenCalledWith('/dashboard');
    });

    it('should show loading state during authentication', async () => {
      const user = userEvent.setup();

      // Mock a delayed response
      mockUseAuth.mockReturnValue({
        signIn: mockSignIn,
        isLoading: false,
        error: null,
      });

      const { rerender } = render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);

      // Rerender with loading state
      mockUseAuth.mockReturnValue({
        signIn: mockSignIn,
        isLoading: true,
        error: null,
      });
      rerender(<LoginForm />);

      // Check that loading state is shown
      expect(screen.getByText(/signing in/i)).toBeInTheDocument();
      expect(submitButton).toBeDisabled();

      // Rerender to finish loading
      mockUseAuth.mockReturnValue({
        signIn: mockSignIn,
        isLoading: false,
        error: null,
      });
      rerender(<LoginForm />);

      // Wait for loading to complete
      await waitFor(() => {
        expect(screen.queryByText(/signing in/i)).not.toBeInTheDocument();
      });
    });
  });

  describe('Failed Login', () => {
    it('should display error message on authentication failure', async () => {
      const user = userEvent.setup();

      // Mock authentication error
      mockSignIn.mockResolvedValue({
        error: { message: 'Invalid login credentials' },
      });
      mockUseAuth.mockReturnValue({
        signIn: mockSignIn,
        isLoading: false,
        error: { message: 'Invalid login credentials' },
      });

      render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'wrongpassword');
      await user.click(submitButton);

      // Wait for error message to appear
      await waitFor(() => {
        expect(screen.getByRole('alert')).toHaveTextContent('Invalid login credentials');
      });

      // Ensure router.push was not called
      expect(mockPush).not.toHaveBeenCalled();
    });

    it('should display fallback error message when error has no message', async () => {
      const user = userEvent.setup();

      // Mock authentication error without message
      mockSignIn.mockResolvedValue({ error: {} });
      mockUseAuth.mockReturnValue({
        signIn: mockSignIn,
        isLoading: false,
        error: { message: 'Invalid login credentials.' },
      });

      render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByRole('alert')).toHaveTextContent('Invalid login credentials.');
      });
    });

    it('should handle unexpected errors during authentication', async () => {
      const user = userEvent.setup();

      mockUseAuth.mockReturnValue({
        signIn: mockSignIn,
        isLoading: false,
        error: null,
      });

      const { rerender } = render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);

      // Rerender with the error
      mockUseAuth.mockReturnValue({
        signIn: mockSignIn,
        isLoading: false,
        error: { message: 'Network error' },
      });
      rerender(<LoginForm />);

      await waitFor(() => {
        expect(screen.getByRole('alert')).toHaveTextContent('Network error');
      });
    });

    it('should handle non-Error exceptions', async () => {
      const user = userEvent.setup();

      mockUseAuth.mockReturnValue({
        signIn: mockSignIn,
        isLoading: false,
        error: null,
      });

      const { rerender } = render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);

      // Rerender with the error
      mockUseAuth.mockReturnValue({
        signIn: mockSignIn,
        isLoading: false,
        error: { message: 'An unexpected error occurred. Please try again.' },
      });
      rerender(<LoginForm />);

      await waitFor(() => {
        expect(screen.getByRole('alert')).toHaveTextContent(
          'An unexpected error occurred. Please try again.'
        );
      });
    });

    it('should clear previous error messages on new submission', async () => {
      const user = userEvent.setup();

      // First submission with error
      mockSignIn.mockResolvedValueOnce({
        error: { message: 'Invalid credentials' },
      });
      mockUseAuth.mockReturnValue({
        signIn: mockSignIn,
        isLoading: false,
        error: { message: 'Invalid credentials' },
      });

      render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'wrongpassword');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByRole('alert')).toHaveTextContent('Invalid credentials');
      });

      // Second submission with success
      mockUseAuth.mockReturnValue({
        signIn: mockSignIn.mockResolvedValueOnce({ error: null }),
        isLoading: false,
        error: null,
      });

      await user.clear(passwordInput);
      await user.type(passwordInput, 'correctpassword');
      await user.click(submitButton);

      // Error message should be cleared
      await waitFor(() => {
        expect(screen.queryByRole('alert')).not.toBeInTheDocument();
      });
    });
  });

  describe('Form Validation', () => {
    it('should show validation error for invalid email format', async () => {
      const user = userEvent.setup();

      render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      // Enter invalid email
      await user.type(emailInput, 'invalid-email');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);

      // Check for validation error
      expect(screen.getByText('Please enter a valid email address.')).toBeInTheDocument();

      // Ensure API was not called
      expect(mockSignIn).not.toHaveBeenCalled();
    });

    it('should show validation error for short password', async () => {
      const user = userEvent.setup();

      render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, '123'); // Too short
      await user.click(submitButton);

      expect(screen.getByText('Password must be at least 6 characters.')).toBeInTheDocument();
      expect(mockSignIn).not.toHaveBeenCalled();
    });

    it('should show validation errors for empty fields', async () => {
      const user = userEvent.setup();

      render(<LoginForm />);

      const submitButton = screen.getByRole('button', { name: /sign in/i });

      // Submit empty form
      await user.click(submitButton);

      // Both validation errors should appear
      expect(screen.getByText('Please enter a valid email address.')).toBeInTheDocument();
      expect(screen.getByText('Password must be at least 6 characters.')).toBeInTheDocument();
      expect(mockSignIn).not.toHaveBeenCalled();
    });

    it('should apply error styling to invalid fields', async () => {
      const user = userEvent.setup();

      render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, 'invalid-email');
      await user.type(passwordInput, '123');
      await user.click(submitButton);

      // Check that error styling is applied
      expect(emailInput).toHaveClass('border-red-500');
      expect(passwordInput).toHaveClass('border-red-500');
    });

    it('should remove validation errors when fields become valid', async () => {
      const user = userEvent.setup();

      render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      // First, trigger validation errors
      await user.click(submitButton);

      expect(screen.getByText('Please enter a valid email address.')).toBeInTheDocument();
      expect(screen.getByText('Password must be at least 6 characters.')).toBeInTheDocument();

      // Then fix the fields
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');

      // Validation errors should be cleared
      await waitFor(() => {
        expect(screen.queryByText('Please enter a valid email address.')).not.toBeInTheDocument();
        expect(
          screen.queryByText('Password must be at least 6 characters.')
        ).not.toBeInTheDocument();
      });

      // Error styling should be removed
      expect(emailInput).not.toHaveClass('border-red-500');
      expect(passwordInput).not.toHaveClass('border-red-500');
    });
  });

  describe('Form Accessibility', () => {
    it('should have proper form structure and labels', () => {
      render(<LoginForm />);

      // Check for proper form structure (form element exists)
      const formElement = screen.getByRole('button', { name: /sign in/i }).closest('form');
      expect(formElement).toBeInTheDocument();

      // Check for proper labels
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();

      // Check for submit button
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
    });

    it('should have proper ARIA attributes during loading', async () => {
      const user = userEvent.setup();

      mockUseAuth.mockReturnValue({
        signIn: mockSignIn,
        isLoading: false,
        error: null,
      });

      const { rerender } = render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);

      // Rerender with loading state
      mockUseAuth.mockReturnValue({
        signIn: mockSignIn,
        isLoading: true,
        error: null,
      });
      rerender(<LoginForm />);

      // Check aria-busy attribute
      expect(submitButton).toHaveAttribute('aria-busy', 'true');

      // Rerender to finish loading
      mockUseAuth.mockReturnValue({
        signIn: mockSignIn,
        isLoading: false,
        error: null,
      });
      rerender(<LoginForm />);

      await waitFor(() => {
        expect(submitButton).toHaveAttribute('aria-busy', 'false');
      });
    });

    it('should have proper role for error messages', async () => {
      const user = userEvent.setup();

      mockSignIn.mockResolvedValue({
        error: { message: 'Invalid credentials' },
      });
      mockUseAuth.mockReturnValue({
        signIn: mockSignIn,
        isLoading: false,
        error: { message: 'Invalid credentials' },
      });

      render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'wrongpassword');
      await user.click(submitButton);

      await waitFor(() => {
        const errorElement = screen.getByRole('alert');
        expect(errorElement).toBeInTheDocument();
        expect(errorElement).toHaveTextContent('Invalid credentials');
      });
    });
  });
});
