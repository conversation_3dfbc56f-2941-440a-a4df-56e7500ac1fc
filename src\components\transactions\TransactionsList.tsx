'use client';

import React from 'react';
import TransactionCard, { Transaction } from '@/components/transactions/TransactionCard';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { useTransactions } from '@/lib/hooks/useTransactions';
import TransactionDetailModal from './TransactionDetailModal';
import CategorySelector from '@/components/categories/CategorySelector';
import BulkCategorizeModal from './BulkCategorizeModal';

/**
 * TransactionsList component displays a paginated list of user transactions.
 * It includes filtering and searching functionality.
 */
export default function TransactionsList() {
  const [selectedTransaction, setSelectedTransaction] = React.useState<Transaction | null>(null);
  const [selectedTransactionIds, setSelectedTransactionIds] = React.useState<string[]>([]);
  const [isBulkModalOpen, setIsBulkModalOpen] = React.useState(false);

  // Filter state
  const [searchQuery, setSearchQuery] = React.useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = React.useState('');
  const [categoryId, setCategoryId] = React.useState('');
  const [startDate, setStartDate] = React.useState('');
  const [endDate, setEndDate] = React.useState('');

  // Debounce search query to avoid API calls on every keystroke
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Memoize filters to prevent unnecessary re-renders
  const filters = React.useMemo(() => {
    const hasFilters = debouncedSearchQuery || categoryId || startDate || endDate;
    if (!hasFilters) return undefined;

    return {
      searchQuery: debouncedSearchQuery || undefined,
      categoryId: categoryId || undefined,
      startDate: startDate || undefined,
      endDate: endDate || undefined,
    };
  }, [debouncedSearchQuery, categoryId, startDate, endDate]);

  // Use the hook with memoized filters
  const { transactions, pagination, isLoading, error, handlePageChange, retryFetch } =
    useTransactions(filters);

  const handleCardClick = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
  };

  const handleToggleSelected = (transactionId: string) => {
    setSelectedTransactionIds((prev) =>
      prev.includes(transactionId)
        ? prev.filter((id) => id !== transactionId)
        : [...prev, transactionId]
    );
  };

  const handleCloseModal = () => {
    setSelectedTransaction(null);
  };

  const handleClearFilters = () => {
    setSearchQuery('');
    setCategoryId('');
    setStartDate('');
    setEndDate('');
  };

  const handleBulkCategorizeSuccess = () => {
    setSelectedTransactionIds([]);
    setIsBulkModalOpen(false);
    retryFetch(); // This will refetch the transactions
  };

  const hasActiveFilters = searchQuery || categoryId || startDate || endDate;

  if (isLoading) {
    return (
      <div className='flex flex-col items-center justify-center py-12'>
        <LoadingSpinner className='mb-4' />
        <p className='text-gray-600'>Loading transactions...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className='flex flex-col items-center justify-center py-12'>
        <div className='bg-red-50 border border-red-200 rounded-lg p-6 max-w-md'>
          <h3 className='text-red-800 font-semibold mb-2'>Error Loading Transactions</h3>
          <p className='text-red-600 mb-4'>{error}</p>
          <button
            onClick={retryFetch}
            className='px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700'
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (transactions.length === 0) {
    return (
      <div className='flex flex-col items-center justify-center py-12'>
        <div className='bg-gray-50 border border-gray-200 rounded-lg p-6 max-w-md text-center'>
          <h3 className='text-gray-800 font-semibold mb-2'>No Transactions Found</h3>
          <p className='text-gray-600'>
            You don&apos;t have any transactions yet. Connect your bank accounts to start importing
            transactions.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Filters Section */}
      <div className='bg-white p-6 rounded-lg border border-gray-200'>
        <h3 className='text-lg font-semibold mb-4'>Filter Transactions</h3>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
          {/* Search Input */}
          <div>
            <label htmlFor='search' className='block text-sm font-medium text-gray-700 mb-1'>
              Search Merchant
            </label>
            <input
              id='search'
              type='text'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder='Search by merchant name...'
              className='block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-indigo-500 focus:ring-indigo-500'
            />
          </div>

          {/* Category Filter */}
          <div>
            <label htmlFor='category' className='block text-sm font-medium text-gray-700 mb-1'>
              Category
            </label>
            <CategorySelector selectedValue={categoryId} onValueChange={setCategoryId} />
          </div>

          {/* Start Date */}
          <div>
            <label htmlFor='startDate' className='block text-sm font-medium text-gray-700 mb-1'>
              Start Date
            </label>
            <input
              id='startDate'
              type='date'
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className='block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-indigo-500 focus:ring-indigo-500'
            />
          </div>

          {/* End Date */}
          <div>
            <label htmlFor='endDate' className='block text-sm font-medium text-gray-700 mb-1'>
              End Date
            </label>
            <input
              id='endDate'
              type='date'
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className='block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-indigo-500 focus:ring-indigo-500'
            />
          </div>
        </div>

        {/* Clear Filters Button */}
        {hasActiveFilters && (
          <div className='mt-4'>
            <button
              onClick={handleClearFilters}
              className='px-4 py-2 text-sm text-gray-600 bg-gray-100 rounded hover:bg-gray-200'
            >
              Clear All Filters
            </button>
          </div>
        )}
      </div>

      {/* Results Summary */}
      {pagination && (
        <div className='text-sm text-gray-600'>
          {hasActiveFilters ? 'Filtered results: ' : 'Showing '}
          {pagination.totalCount} transaction{pagination.totalCount !== 1 ? 's' : ''}
          {hasActiveFilters && ' found'}
        </div>
      )}

      {/* Bulk Actions */}
      {selectedTransactionIds.length > 0 && (
        <div className='bg-gray-100 p-4 rounded-lg border border-gray-200 flex items-center justify-between'>
          <span className='text-sm font-medium text-gray-700'>
            {selectedTransactionIds.length} transaction
            {selectedTransactionIds.length > 1 ? 's' : ''} selected
          </span>
          <button
            onClick={() => setIsBulkModalOpen(true)}
            className='px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700'
          >
            Categorize Selected
          </button>
        </div>
      )}

      {/* Transactions List */}
      <div className='space-y-4'>
        {transactions.map((transaction) => (
          <TransactionCard
            key={transaction.id}
            transaction={transaction}
            onClick={handleCardClick}
            isSelected={selectedTransactionIds.includes(transaction.id)}
            onToggleSelected={handleToggleSelected}
          />
        ))}
      </div>

      {selectedTransaction && (
        <TransactionDetailModal transaction={selectedTransaction} onClose={handleCloseModal} />
      )}

      {isBulkModalOpen && (
        <BulkCategorizeModal
          transactionIds={selectedTransactionIds}
          onClose={() => setIsBulkModalOpen(false)}
          onSuccess={handleBulkCategorizeSuccess}
        />
      )}

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className='flex items-center justify-between border-t pt-6'>
          <div className='text-sm text-gray-600'>
            Showing {(pagination.currentPage - 1) * pagination.pageSize + 1} to{' '}
            {Math.min(pagination.currentPage * pagination.pageSize, pagination.totalCount)} of{' '}
            {pagination.totalCount} transactions
          </div>

          <div className='flex items-center space-x-2'>
            <button
              onClick={() => handlePageChange(pagination.currentPage - 1)}
              disabled={!pagination.hasPreviousPage}
              className='px-3 py-2 text-sm border rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed'
            >
              Previous
            </button>

            <span className='px-3 py-2 text-sm'>
              Page {pagination.currentPage} of {pagination.totalPages}
            </span>

            <button
              onClick={() => handlePageChange(pagination.currentPage + 1)}
              disabled={!pagination.hasNextPage}
              className='px-3 py-2 text-sm border rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed'
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
