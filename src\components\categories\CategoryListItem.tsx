'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Category } from '@/lib/hooks/useCategories';

interface CategoryListItemProps {
  category: Category;
  onEdit: (category: Category) => void;
  onDelete: (category: Category) => void;
}

export default function CategoryListItem({ category, onEdit, onDelete }: CategoryListItemProps) {
  return (
    <div className='flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50'>
      <div className='flex items-center gap-3'>
        <div
          className='w-6 h-6 rounded-full flex items-center justify-center text-white text-sm'
          style={{ backgroundColor: category.color || '#6b7280' }}
        >
          {category.icon || '📁'}
        </div>
        <div>
          <h3 className='font-medium'>{category.name}</h3>
          {category.description && <p className='text-sm text-gray-600'>{category.description}</p>}
        </div>
      </div>
      <div className='flex gap-2'>
        <Button variant='outline' size='sm' onClick={() => onEdit(category)}>
          Edit
        </Button>
        <Button variant='destructive' size='sm' onClick={() => onDelete(category)}>
          Delete
        </Button>
      </div>
    </div>
  );
}
