import { GET } from '../get/route';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { getTransactions } from '@/lib/services/transactionService';
import { NextRequest } from 'next/server';

// Mock dependencies
jest.mock('@/lib/supabase/server', () => ({
  createSupabaseServerClient: jest.fn(),
}));

jest.mock('@/lib/services/transactionService', () => ({
  getTransactions: jest.fn(),
}));

jest.mock('next/server', () => ({
  ...jest.requireActual('next/server'),
  NextResponse: {
    json: jest.fn((body, init) => ({
      status: init?.status || 200,
      json: () => Promise.resolve(body),
    })),
  },
}));

describe('GET /api/transactions', () => {
  const mockUser = { id: 'test-user-id' };

  const createMockRequest = (queryParams: Record<string, string> = {}) => {
    const url = new URL('http://localhost/api/transactions');
    Object.entries(queryParams).forEach(([key, value]) => {
      url.searchParams.set(key, value);
    });
    return new Request(url.toString(), {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
    }) as NextRequest;
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('Success (200 OK) with pagination', async () => {
    const mockTransactions = Array.from({ length: 10 }, (_, i) => ({
      id: `txn-${i}`,
      amount: 100 + i,
      description: `Test Transaction ${i}`,
      transaction_date: new Date().toISOString(),
    }));
    const mockTotalCount = 25;

    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    });

    (getTransactions as jest.Mock).mockResolvedValue({
      transactions: mockTransactions,
      totalCount: mockTotalCount,
    });

    const req = createMockRequest({ page: '2', pageSize: '10' });
    const res = await GET(req);
    const body = await res.json();

    expect(res.status).toBe(200);
    expect(body.transactions).toEqual(mockTransactions);
    expect(body.pagination).toEqual({
      currentPage: 2,
      pageSize: 10,
      totalCount: mockTotalCount,
      totalPages: 3,
      hasNextPage: true,
      hasPreviousPage: true,
    });
    expect(getTransactions).toHaveBeenCalledWith(expect.any(Object), mockUser.id, 2, 10, {
      searchQuery: undefined,
      categoryId: undefined,
      startDate: undefined,
      endDate: undefined,
    });
  });

  test('Unauthorized (401)', async () => {
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest
          .fn()
          .mockResolvedValue({ data: { user: null }, error: { message: 'Unauthorized' } }),
      },
    });

    const req = createMockRequest();
    const res = await GET(req);

    expect(res.status).toBe(401);
    expect(await res.json()).toEqual({ error: 'Unauthorized' });
    expect(getTransactions).not.toHaveBeenCalled();
  });

  test('Service Error (500)', async () => {
    const serviceError = new Error('Service failed');
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    });
    (getTransactions as jest.Mock).mockRejectedValue(serviceError);

    // Suppress console.error for this test
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    const req = createMockRequest();
    const res = await GET(req);

    expect(res.status).toBe(500);
    expect(await res.json()).toEqual({ error: 'Service failed' });
    expect(getTransactions).toHaveBeenCalledWith(expect.any(Object), mockUser.id, 1, 20, {
      searchQuery: undefined,
      categoryId: undefined,
      startDate: undefined,
      endDate: undefined,
    });

    consoleErrorSpy.mockRestore();
  });

  test('Invalid pagination parameters (400)', async () => {
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    });

    const req = createMockRequest({ page: '0', pageSize: '200' });
    const res = await GET(req);

    expect(res.status).toBe(400);
    expect(await res.json()).toEqual({
      error:
        'Invalid pagination parameters. Page must be >= 1 and pageSize must be between 1 and 100.',
    });
    expect(getTransactions).not.toHaveBeenCalled();
  });
});
