import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

import { SupabaseClient } from '@supabase/supabase-js';

export async function verifyOwnership(
  supabase: SupabaseClient,
  userId: string,
  tableName: string,
  recordId: string
): Promise<boolean> {
  const { data, error } = await supabase
    .from(tableName)
    .select('user_id')
    .eq('id', recordId)
    .single();

  if (error || !data) {
    return false;
  }

  return data.user_id === userId;
}
