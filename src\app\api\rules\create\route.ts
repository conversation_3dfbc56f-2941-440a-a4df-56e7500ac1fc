import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { z } from 'zod';

export async function POST(request: NextRequest) {
  const supabase = await createSupabaseServerClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const userId = user.id;

  try {
    const createRuleSchema = z.object({
      rule_type: z.string().trim().min(1),
      match_criteria: z.string().trim().min(1),
      category_id: z.string().uuid(),
    });

    const body = await request.json();
    const validation = createRuleSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json({ error: validation.error.format() }, { status: 400 });
    }

    const { rule_type, match_criteria, category_id } = validation.data;

    const { data: rule, error: insertError } = await supabase
      .from('category_rules')
      .insert({
        user_id: userId,
        rule_type,
        match_criteria,
        category_id,
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error creating rule:', insertError);
      return NextResponse.json({ error: 'Failed to create rule' }, { status: 500 });
    }

    return NextResponse.json({ rule }, { status: 201 });
  } catch (error) {
    console.error('[RULES_CREATE_ERROR]', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
