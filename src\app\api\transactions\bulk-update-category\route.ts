import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { bulkUpdateTransactionCategory } from '@/lib/services/transactionService';

export async function POST(request: NextRequest) {
  const supabase = await createSupabaseServerClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  let transactionIds: string[];
  let categoryId: string;

  try {
    const body = await request.json();
    transactionIds = body.transactionIds;
    categoryId = body.categoryId;

    if (!Array.isArray(transactionIds) || transactionIds.length === 0 || !categoryId) {
      throw new Error('transactionIds must be a non-empty array and categoryId is required');
    }
  } catch (e) {
    const error = e as Error;
    return NextResponse.json({ error: `Invalid request body: ${error.message}` }, { status: 400 });
  }

  try {
    await bulkUpdateTransactionCategory(supabase, transactionIds, categoryId);
    return new NextResponse(null, { status: 200 });
  } catch (error) {
    console.error('Error bulk updating transaction category:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
