import { GET } from '../get/route';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { SupabaseClient } from '@supabase/supabase-js';

jest.mock('@/lib/supabase/server');
jest.mock('next/headers', () => ({
  cookies: jest.fn().mockReturnValue({
    getAll: jest.fn(),
    set: jest.fn(),
  }),
}));

const mockCreateSupabaseServerClient = createSupabaseServerClient as jest.Mock;

describe('GET /api/categories', () => {
  const mockUser = { id: 'user-123' };
  const mockUserCategories = [
    { id: 'usr-1', name: 'Freelance Income', user_id: 'user-123', is_active: true },
    { id: 'usr-2', name: 'Side Hustle', user_id: 'user-123', is_active: true },
    { id: 'usr-3', name: 'Groceries', user_id: 'user-123', is_active: true },
    { id: 'usr-4', name: 'Utilities', user_id: 'user-123', is_active: true },
  ].sort((a, b) => a.name.localeCompare(b.name));

  let mockSupabaseClient: Partial<SupabaseClient>;

  beforeEach(() => {
    const mockAuth = {
      getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
    };

    const genericQueryBuilder = {
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      in: jest.fn().mockReturnThis(),
      single: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      or: jest.fn().mockReturnThis(),
      then: (resolve: any) => resolve({ data: [], error: null }),
    };

    mockSupabaseClient = {
      auth: mockAuth as any,
      from: jest.fn().mockReturnValue(genericQueryBuilder),
      rpc: jest.fn().mockReturnValue(genericQueryBuilder),
    };

    mockCreateSupabaseServerClient.mockResolvedValue(mockSupabaseClient as any);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('Success (200 OK) returns sorted categories', async () => {
    const mockQueryBuilder = {
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockResolvedValue({ data: mockUserCategories, error: null }),
    };
    (mockSupabaseClient.from as jest.Mock).mockReturnValue(mockQueryBuilder);

    const response = await GET();
    const json = await response.json();

    expect(response.status).toBe(200);
    expect(json.categories).toEqual(mockUserCategories);
    expect(mockSupabaseClient.auth!.getUser).toHaveBeenCalledTimes(1);
    expect(mockSupabaseClient.from).toHaveBeenCalledWith('user_categories');
    expect(mockQueryBuilder.eq).toHaveBeenCalledWith('user_id', mockUser.id);
    expect(mockQueryBuilder.eq).toHaveBeenCalledWith('is_active', true);
    expect(mockQueryBuilder.order).toHaveBeenCalledWith('name', { ascending: true });
  });

  test('Unauthorized (401) returns error when no user', async () => {
    (mockSupabaseClient.auth!.getUser as jest.Mock).mockResolvedValueOnce({
      data: { user: null },
      error: null,
    });

    const response = await GET();
    const json = await response.json();

    expect(response.status).toBe(401);
    expect(json.error).toBe('Unauthorized');
  });

  test('Error (500) when fetching categories fails', async () => {
    const dbError = { message: 'DB error', code: '500' };
    const mockQueryBuilder = {
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockResolvedValue({ data: null, error: dbError }),
    };
    (mockSupabaseClient.from as jest.Mock).mockReturnValue(mockQueryBuilder);

    const response = await GET();
    const json = await response.json();

    expect(response.status).toBe(500);
    expect(json.error).toBe('Failed to fetch categories');
  });
});
