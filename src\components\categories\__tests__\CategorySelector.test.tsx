import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import CategorySelector from '../CategorySelector';

describe('CategorySelector', () => {
  const categoriesMock = [
    { id: '1', name: 'Food' },
    { id: '2', name: 'Transport' },
    { id: '3', name: 'Entertainment' },
  ];

  beforeEach(() => {
    global.fetch = jest.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ categories: categoriesMock }),
    });
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  test('renders dropdown with categories and pre-selected value', async () => {
    const onValueChange = jest.fn();
    render(<CategorySelector selectedValue='2' onValueChange={onValueChange} />);

    await waitFor(() => {
      expect(screen.getByRole('combobox')).toBeInTheDocument();
    });

    const select = screen.getByRole('combobox');
    expect((select as HTMLSelectElement).value).toBe('2');

    categoriesMock.forEach((category) => {
      expect(screen.getByRole('option', { name: category.name })).toBeInTheDocument();
    });
  });

  test('calls onValueChange when a new category is selected', async () => {
    const onValueChange = jest.fn();
    render(<CategorySelector selectedValue='1' onValueChange={onValueChange} />);

    await waitFor(() => {
      expect(screen.getByRole('combobox')).toBeInTheDocument();
    });

    const select = screen.getByRole('combobox');
    fireEvent.change(select, { target: { value: '3' } });

    expect(onValueChange).toHaveBeenCalledWith('3');
  });

  test('displays an error message if fetching categories fails', async () => {
    global.fetch = jest.fn().mockRejectedValue(new Error('Failed to fetch'));
    const onValueChange = jest.fn();
    render(<CategorySelector selectedValue='' onValueChange={onValueChange} />);

    await waitFor(() => {
      expect(screen.getByText(/Error: Failed to fetch/i)).toBeInTheDocument();
    });
  });

  test('is disabled when the disabled prop is true', async () => {
    const onValueChange = jest.fn();
    render(<CategorySelector selectedValue='1' onValueChange={onValueChange} disabled />);

    await waitFor(() => {
      expect(screen.getByRole('combobox')).toBeInTheDocument();
    });

    const select = screen.getByRole('combobox');
    expect(select).toBeDisabled();
  });
});
