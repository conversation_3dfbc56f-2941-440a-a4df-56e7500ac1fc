name: NAVsync CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  build-and-test:
    if: github.event_name != 'pull_request' || github.event.pull_request.head.repo.full_name == github.repository
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [22.x]

    env:
      PLAID_CLIENT_ID: ${{ secrets.PLAID_CLIENT_ID }}
      PLAID_SECRET_SANDBOX: ${{ secrets.PLAID_SECRET_SANDBOX }}
      PLAID_SECRET_DEVELOPMENT: ${{ secrets.PLAID_SECRET_DEVELOPMENT }}
      PLAID_ENV: ${{ secrets.PLAID_ENV }}
      NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
      NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linters
        run: npm run lint

      - name: Run type checking
        run: npm run type-check

      - name: Run security audit
        run: npm run audit
