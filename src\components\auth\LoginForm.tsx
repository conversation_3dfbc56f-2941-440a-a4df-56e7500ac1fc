'use client';

import * as React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/hooks/useAuth';
// shadcn/ui components
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';

// Zod schema for form validation
const loginSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  password: z.string().min(6, { message: 'Password must be at least 6 characters.' }),
});

export type Credentials = z.infer<typeof loginSchema>;

export function LoginForm() {
  const router = useRouter();
  const { signIn, isLoading, error: apiError } = useAuth();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<Credentials>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = async (data: Credentials) => {
    await signIn(data);
    if (!apiError) {
      router.push('/dashboard');
    }
  };

  return (
    <Card className='max-w-md w-full mx-auto mt-8 shadow-lg'>
      <CardHeader>
        <CardTitle className='text-center text-2xl font-bold'>Sign in to NAVsync.io</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className='space-y-6' autoComplete='off' noValidate>
          <div className='space-y-2'>
            <Label htmlFor='email'>Email</Label>
            <Input
              id='email'
              type='email'
              autoComplete='email'
              placeholder='<EMAIL>'
              disabled={isLoading}
              {...register('email')}
              className={errors.email ? 'border-red-500' : ''}
            />
            {errors.email && <div className='text-red-600 text-sm'>{errors.email.message}</div>}
          </div>

          <div className='space-y-2'>
            <Label htmlFor='password'>Password</Label>
            <Input
              id='password'
              type='password'
              autoComplete='current-password'
              placeholder='Your password'
              disabled={isLoading}
              {...register('password')}
              className={errors.password ? 'border-red-500' : ''}
            />
            {errors.password && (
              <div className='text-red-600 text-sm'>{errors.password.message}</div>
            )}
          </div>

          <div className='text-right'>
            <Link href='/reset-password' className='text-sm underline'>
              Forgot password?
            </Link>
          </div>

          {apiError && (
            <div className='text-red-600 text-sm text-center' role='alert'>
              {apiError?.message}
            </div>
          )}

          <Button type='submit' className='w-full' disabled={isLoading} aria-busy={isLoading}>
            {isLoading ? (
              <span>
                <span className='animate-spin inline-block mr-2'>&#9696;</span>
                Signing in...
              </span>
            ) : (
              'Sign In'
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}

export default LoginForm;
