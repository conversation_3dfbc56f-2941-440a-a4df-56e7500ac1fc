# NAVsync.io Points & Rewards System Specification

**Version:** 1.0
**Date:** May 21, 2025
**Author:** <PERSON><PERSON> (AI Technical Leader) & User

## 1. Overview & Goals

This document outlines the specification for the NAVsync.io Points & Rewards System, designed to enhance user engagement, encourage positive financial habits, and provide a basis for community features.

**Primary Goals:**

*   Incentivize consistent and thoughtful budgeting.
*   Reward users for achieving their financial targets at both group and overall levels.
*   Foster a sense of accomplishment and make financial management more engaging.
*   Ensure fairness and transparency in point allocation.
*   Provide a foundation for future opt-in community benchmarking and leaderboards.
*   Maintain a positive user experience by ensuring total monthly scores do not go below zero.

## 2. Core System Mechanics

### 2.1. Monthly Engagement Bonus
*   **Points:** +1000 points.
*   **Awarded:** Automatically each month to users who actively participate in the budgeting system (e.g., have active budgets set for the default groups). Specific engagement criteria to be finalized (e.g., logging in, reviewing budgets).

### 2.2. Budget Performance Points Pool
*   **Total Pool:** 1000 points.
*   **Distribution:** This pool is notionally distributed equally among all active budget groups a user has (default + user-defined). This determines the "base potential points" for each group if its budget is met exactly.
    *   Example: If a user has 5 active groups, each group has a base potential of 1000 / 5 = 200 points.

### 2.3. Budget Groups
*   **Definition:** A user-defined collection of individual spending/saving categories.
*   **Group Budget:** The sum of the budgets of all categories within that group.
*   **Default Groups:** 5 predefined, mandatory budget groups. Users map their custom categories to these defaults.
    1.  **Housing:** (e.g., Rent/Mortgage, Property Taxes, Home Insurance, Maintenance)
    2.  **Food & Groceries:** (e.g., Groceries, Restaurants, Coffee Shops)
    3.  **Transportation:** (e.g., Fuel, Public Transit, Car Payments, Insurance, Maintenance)
    4.  **Discretionary:** (e.g., Entertainment, Shopping, Hobbies, Personal Care)
    5.  **Savings & Debt:** (e.g., General Savings, Emergency Fund, Investment Contributions, Credit Card Payments, Loan Repayments)
*   **User-Defined Groups:** Users can create additional custom groups. The 1000 Budget Performance Points Pool will be divided among all active groups (default + custom).

### 2.4. Budgeting Cycle & Lock-in
*   **Budget Lock-in:** Budgets for categories (and thus groups) are locked for point calculation purposes after the first week of the month.
*   **Rollover Funds:** Rollover allocations (if any) must be completed by mid-month. Points are calculated based on the group's budget *after* these rollovers are finalized.
*   **Handling of Late Budget Adjustments:** Users retain the flexibility to modify their actual budget amounts or reallocate funds within the NAVsync.io platform at any time during the month for their personal financial management needs. However, for the purposes of the current month's Points & Rewards System calculations, any such changes made *after* the defined cutoff dates (i.e., after the first week for budget amounts, and after mid-month for rollover allocations) will **not** influence that month's point calculations. The points system will operate based on the "snapshot" of the budget as it stood at these cutoff times. This ensures fairness and predictability in the points system while maintaining flexibility for real-world financial adjustments.

## 3. Calculating Points for Individual Budget Groups

The score for each individual budget group is calculated based on its performance relative to its allocated share of the 1000 Budget Performance Points Pool.

Let `GroupBasePotential` = (1000 Budget Performance Points Pool / Number of Active Groups).

### 3.1. Meeting Budget (Expense Groups & Savings/Debt Groups)
*   If a group's `Actual Spending/Saving = Budgeted Amount` (0% over/under).
*   **Score Contribution:** `+ GroupBasePotential`

### 3.2. Under Budget (Expense Groups) / Over-Achieving (Savings/Debt Groups)
*   Applies if `Actual Spending < Budgeted Amount` (for expense groups) or `Actual Saved/Invested > Budgeted Amount` (for Savings/Debt groups).
*   Let `PercentUnder` = Percentage by which the group is under budget (or over-saved/invested).
*   **Bonus Points:** `(PercentUnder * 0.01) * GroupBasePotential`
    *   This bonus is capped when `PercentUnder` = 30% (i.e., max bonus is +30% of `GroupBasePotential`).
*   **Score Contribution:** `GroupBasePotential + Bonus Points` (Max score for a group is 130% of its `GroupBasePotential`).

### 3.3. Over Budget (Expense Groups) / Under-Achieving (Savings/Debt Groups)
*   Applies if `Actual Spending > Budgeted Amount` (for expense groups) or `Actual Saved/Invested < Budgeted Amount` (for Savings/Debt groups).
*   Let `PercentOver` = Percentage by which the group is over budget (or under-saved/invested).
*   **Penalty Points:** `(PercentOver * 0.04) * GroupBasePotential`
*   **Score Contribution:** `GroupBasePotential - Penalty Points`
    *   If `PercentOver` = 25%, the penalty is 100% of `GroupBasePotential`, so score contribution is 0.
    *   **Cap on Negative Contribution:** A single group's negative score contribution is capped at -100% of its `GroupBasePotential`. (e.g., if `GroupBasePotential` is 200, the most this group can negatively contribute is -200 points, which occurs if `PercentOver` >= 50%).

## 4. Calculating Overall Spending Performance Bonus

This bonus is applied if the user meets overall financial targets for the month.

### 4.1. Trigger Conditions
1.  `Total Actual Spending <= Total Budgeted Spending` (across all categories/groups for the month).
2.  `Total Budgeted Spending < Verified Typical Monthly Income`.
    *   "Typical Monthly Income" is initially user-inputted.
    *   AI will analyze linked account data (with user consent, as per PRD 4.7.1, 4.7.10) to flag if the user-inputted income seems significantly unrealistic compared to observed income patterns, prompting user review.

### 4.2. Bonus Tiers & Application
*   Let `SumOfQualifyingGroupPoints` = Sum of points earned from all groups that were *either strictly under budget OR met their budget exactly (0% over/under)*. Groups that went over budget do not contribute to this sum for bonus calculation.
*   The bonus percentage is applied to `SumOfQualifyingGroupPoints`.
*   **Bonus Tiers (based on % Total Budget is Under Total Actual Spending):**
    *   **Tier 1 (0% - 9.99% under overall budget):** `+10%` of `SumOfQualifyingGroupPoints`
    *   **Tier 2 (10% - 19.99% under overall budget):** `+15%` of `SumOfQualifyingGroupPoints`
    *   **Tier 3 (20%+ under overall budget):** `+20%` of `SumOfQualifyingGroupPoints`
*   This calculated bonus amount is added to the sum of all individual group scores.

## 5. Calculating Total Monthly Score

1.  **Calculate Individual Group Scores:** Determine the point contribution (positive or negative, capped at -100% of base) for each active budget group based on its performance.
2.  **Sum All Group Scores:** `InitialPerformancePoints = Sum of all Individual Group Scores`.
3.  **Calculate Overall Spending Performance Bonus:** If conditions are met, calculate the bonus amount based on the tiers and apply it to the points from qualifying groups. `CalculatedOverallBonus = Bonus % * SumOfQualifyingGroupPoints`.
4.  **Total Budget Performance Points:** `TotalPerformancePoints = InitialPerformancePoints + CalculatedOverallBonus`.
    *   Note: Due to the -100% cap on individual group negative contributions, the `InitialPerformancePoints` (and thus `TotalPerformancePoints` before the engagement bonus) cannot be less than -1000 (if the 1000 Budget Performance Points Pool is used and there are 5 groups each contributing -200, or equivalent for more groups).
5.  **Final Total Monthly Score:**
    `FinalScore = Monthly Engagement Bonus (1000) + TotalPerformancePoints`.
    *   This ensures the minimum possible `FinalScore` is 0.

## 6. Example Scenario: "The Steady Saver" (Illustrative)

*   **Setup:** 5 Default Groups. `GroupBasePotential` = 200 points each.
*   **Overall Performance:** 5% under total budget (Overall Bonus Tier 1: +10%).
*   **Income Condition:** Met.

| Group         | Budget | Actual | Perf. vs Budget                                     | Base Score | Group Bonus/Penalty Calc (Points) | Group Score | Qualifies for Overall Bonus? |
| :------------ | :----- | :----- | :-------------------------------------------------- | :--------- | :-------------------------------- | :---------- | :--------------------------- |
| Housing       | $1500  | $1500  | 0% (Met)                                            | 200        | 0                                 | 200         | Yes                          |
| Food          | $800   | $720   | 10% Under                                           | 200        | + (0.10 \* 200) = +20             | 220         | Yes                          |
| Transport     | $500   | $450   | 10% Under                                           | 200        | + (0.10 \* 200) = +20             | 220         | Yes                          |
| Discretionary | $700   | $770   | 10% Over                                            | 200        | - (0.10 \* 4% of 200 \* 10) = -80 | 120         | No                           |
| Savings       | $500   | $360   | Saved $140 less (28% "over" savings target)         | 200        | - (0.28 \* 4% of 200 \* 28) = -224| -24         | No                           |
| **InitialPerformancePoints (Sum of Group Scores):**                                                                                   |            | **736**     |                              |
| **SumOfQualifyingGroupPoints (Housing + Food + Transport):** 200 + 220 + 220 = **640**                                                 |            |             |                              |
| **CalculatedOverallBonus (Tier 1: +10% on 640):**                                                                                     |            | **+64**     |                              |
| **TotalPerformancePoints (736 + 64):**                                                                                                |            | **800**     |                              |
| **Monthly Engagement Bonus:**                                                                                                         |            | **+1000**   |                              |
| **FINAL TOTAL MONTHLY SCORE:**                                                                                                        |            | **1800**    |                              |

*(Note: Example calculation for Discretionary: 10% over * 4% penalty per 1% = 40% penalty. 40% of 200 = 80. Score = 200-80=120. Savings: 28% "over" * 4% penalty per 1% = 112% penalty. 112% of 200 = 224. Score = 200-224 = -24.)*

## 7. Community Rankings & Leaderboards
*   To be based on the `FinalTotalMonthlyScore`.
*   Strictly opt-in by users.
*   All data presented will be anonymized.
*   Peer group comparisons may be offered based on voluntary, anonymized demographic data (aligning with PRD 4.8).

## 8. Future Considerations & Open Questions
*   Specific UI/UX for displaying points and progress.
*   Detailed criteria for earning the "Monthly Engagement Bonus."
*   Potential for non-monetary rewards or badges tied to point milestones or streaks.
*   Refinement of point values or bonus percentages after initial user data is available.
*   Handling of new users joining mid-month.

This document provides the foundational rules for the NAVsync.io Points & Rewards System.