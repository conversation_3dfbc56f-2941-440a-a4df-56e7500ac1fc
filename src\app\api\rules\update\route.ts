import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { z } from 'zod';

export async function PUT(request: NextRequest) {
  const supabase = await createSupabaseServerClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const userId = user.id;

  try {
    // Get rule ID from query parameter
    const url = new URL(request.url);
    const rule_id = url.searchParams.get('id');

    if (!rule_id) {
      return NextResponse.json({ error: 'Rule ID is required' }, { status: 400 });
    }

    const updateRuleSchema = z.object({
      rule_type: z.string().trim().min(1).optional(),
      match_criteria: z.string().trim().min(1).optional(),
      category_id: z.string().uuid().optional(),
    });

    const body = await request.json();
    const validation = updateRuleSchema.safeParse(body);

    if (!validation.success) {
      const errorMessage = validation.error.errors.map((e) => e.message).join(', ');
      return NextResponse.json({ error: `Invalid input: ${errorMessage}` }, { status: 400 });
    }

    const updateData = validation.data;

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json({ error: 'No fields to update' }, { status: 400 });
    }

    const { data: rule, error: updateError } = await supabase
      .from('category_rules')
      .update(updateData)
      .eq('id', rule_id)
      .eq('user_id', userId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating rule:', updateError);
      return NextResponse.json({ error: 'Failed to update rule' }, { status: 500 });
    }

    return NextResponse.json({ rule });
  } catch (error) {
    console.error('[RULES_UPDATE_ERROR]', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
