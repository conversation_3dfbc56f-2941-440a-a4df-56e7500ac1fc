import { renderHook, act, waitFor } from '@testing-library/react';
import { useCategories } from '../useCategories';

// Mock global fetch
global.fetch = jest.fn();
global.confirm = jest.fn();

describe('useCategories Hook', () => {
  const mockCategories = [
    {
      id: 'cat-1',
      name: 'Food & Dining',
      description: 'Restaurant and food purchases',
      color: '#ef4444',
      icon: '🍔',
      is_active: true,
      created_at: '2025-01-01T00:00:00Z',
      updated_at: '2025-01-02T00:00:00Z',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    (global.confirm as jest.Mock).mockReturnValue(true);
  });

  describe('Initial State', () => {
    test('initializes with correct default values', () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ categories: [] }),
      });

      const { result } = renderHook(() => useCategories());

      expect(result.current.categories).toEqual([]);
      expect(result.current.isLoading).toBe(true);
      expect(result.current.isSubmitting).toBe(false);
      expect(result.current.error).toBe(null);
      expect(result.current.editingCategory).toBe(null);
    });
  });

  describe('fetchCategories', () => {
    test('fetches categories successfully', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ categories: mockCategories }),
      });

      const { result } = renderHook(() => useCategories());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.categories).toEqual(mockCategories);
      expect(result.current.error).toBe(null);
      expect(global.fetch).toHaveBeenCalledWith('/api/categories/get');
    });

    test('handles fetch error', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
      });

      const { result } = renderHook(() => useCategories());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.error).toBe('Failed to fetch categories');
      expect(result.current.categories).toEqual([]);
    });
  });

  describe('createCategory', () => {
    test('creates category successfully', async () => {
      (global.fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ categories: [] }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ categories: [mockCategories[0]] }),
        });

      const { result } = renderHook(() => useCategories());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      const categoryData = {
        name: 'Test Category',
        description: 'Test Description',
        color: '#ef4444',
        icon: '🍔',
      };

      await act(async () => {
        await result.current.createCategory(categoryData);
      });

      expect(global.fetch).toHaveBeenCalledWith('/api/categories/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(categoryData),
      });
    });
  });

  describe('setEditingCategory', () => {
    test('sets editing category', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ categories: mockCategories }),
      });

      const { result } = renderHook(() => useCategories());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      act(() => {
        result.current.setEditingCategory(mockCategories[0]);
      });

      expect(result.current.editingCategory).toEqual(mockCategories[0]);
    });
  });

  describe('clearError', () => {
    test('clears error state', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
      });

      const { result } = renderHook(() => useCategories());

      await waitFor(() => {
        expect(result.current.error).toBe('Failed to fetch categories');
      });

      act(() => {
        result.current.clearError();
      });

      expect(result.current.error).toBe(null);
    });
  });
});
