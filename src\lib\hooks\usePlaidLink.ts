'use client';

import { useState, useEffect } from 'react';
import {
  usePlaidLink as useReactPlaidLink,
  PlaidLinkOnSuccess,
  PlaidLinkOnExit,
  PlaidLinkOnSuccessMetadata,
  PlaidLinkError,
} from 'react-plaid-link';

interface UsePlaidLinkProps {
  userId: string;
  onSuccess?: (public_token: string, metadata: PlaidLinkOnSuccessMetadata) => Promise<void>;
  onExit?: PlaidLinkOnExit;
}

interface LinkTokenResponse {
  link_token: string;
}

export const usePlaidLink = ({ userId, onSuccess, onExit }: UsePlaidLinkProps) => {
  const [linkToken, setLinkToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | PlaidLinkError | null>(null);

  useEffect(() => {
    const fetchLinkToken = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const response = await fetch('/api/plaid/create-link-token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ userId }),
        });

        if (!response.ok) {
          throw new Error(`Failed to create link token: ${response.statusText}`);
        }

        const data: LinkTokenResponse = await response.json();
        setLinkToken(data.link_token);
      } catch (err) {
        setError(err as Error);
        console.error('Error fetching link token:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLinkToken();
  }, [userId]);

  const handleSuccess: PlaidLinkOnSuccess = async (
    public_token: string,
    metadata: PlaidLinkOnSuccessMetadata
  ) => {
    try {
      const response = await fetch('/api/plaid/exchange-public-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          public_token,
          metadata,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to exchange public token: ${response.statusText}`);
      }

      const onboardingResponse = await fetch('/api/profile/update-onboarding', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      if (!onboardingResponse.ok) {
        const errorData = await onboardingResponse.json();
        throw new Error(errorData.error || 'Failed to update onboarding status');
      }

      if (onSuccess) {
        await onSuccess(public_token, metadata);
      }
    } catch (err) {
      setError(err as Error);
      console.error('Error exchanging public token:', err);
    }
  };

  const handleExit: PlaidLinkOnExit = (err, metadata) => {
    if (err) {
      setError(err);
      console.error('Plaid Link error:', err);
    }
    if (onExit) {
      onExit(err, metadata);
    }
  };

  const config = {
    token: linkToken,
    onSuccess: handleSuccess,
    onExit: handleExit,
  };

  const { open, ready } = useReactPlaidLink(config);

  return {
    open,
    ready,
    isLoading,
    error,
    linkToken,
  };
};
