'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Category } from '@/lib/hooks/useCategories';
import CategoryListItem from './CategoryListItem';

interface CategoryListProps {
  categories: Category[];
  onEdit: (category: Category) => void;
  onDelete: (category: Category) => void;
}

export default function CategoryList({ categories, onEdit, onDelete }: CategoryListProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Your Categories</CardTitle>
        <CardDescription>Manage your custom spending categories</CardDescription>
      </CardHeader>
      <CardContent>
        {categories.length === 0 ? (
          <p className='text-gray-500 text-center py-8'>
            No categories created yet. Create your first category above!
          </p>
        ) : (
          <div className='space-y-3'>
            {categories.map((category) => (
              <CategoryListItem
                key={category.id}
                category={category}
                onEdit={onEdit}
                onDelete={onDelete}
              />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
