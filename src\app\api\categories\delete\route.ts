import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { deleteCategory, CategoryDeletionError } from '@/lib/services/categoryService';

export async function DELETE(request: NextRequest) {
  const supabase = await createSupabaseServerClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { category_id } = await request.json();

    if (!category_id || typeof category_id !== 'string') {
      return NextResponse.json(
        { error: 'Category ID is required and must be a string' },
        { status: 400 }
      );
    }

    const deletedCategory = await deleteCategory(supabase, category_id, user.id);

    return NextResponse.json({
      message: 'Category deleted successfully',
      deleted_category: deletedCategory,
    });
  } catch (error) {
    if (error instanceof CategoryDeletionError) {
      return NextResponse.json(
        { error: error.message, details: error.details },
        { status: error.status }
      );
    }
    console.error('[CATEGORIES_DELETE_ERROR]', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
