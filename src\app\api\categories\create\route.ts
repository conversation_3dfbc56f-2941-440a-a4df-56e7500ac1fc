import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { z } from 'zod';

export async function POST(request: NextRequest) {
  const supabase = await createSupabaseServerClient();

  // Check authentication
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const userId = user.id;

  try {
    const createCategorySchema = z.object({
      name: z.string().trim().min(1, { message: 'Name is required' }),
      color: z.string().optional(),
      icon: z.string().optional(),
      description: z.string().optional(),
    });
    // Parse request body
    const body = await request.json();
    const validation = createCategorySchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json({ error: validation.error.format() }, { status: 400 });
    }

    const { name, color, icon, description } = validation.data;

    // Insert new category
    const { data: category, error: insertError } = await supabase
      .from('user_categories')
      .insert({
        user_id: userId,
        name: name.trim(),
        color: color || null,
        icon: icon || null,
        description: description || null,
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error creating category:', insertError);

      // Handle unique constraint violation
      if (insertError.code === '23505') {
        return NextResponse.json(
          { error: 'A category with this name already exists' },
          { status: 409 }
        );
      }

      return NextResponse.json({ error: 'Failed to create category' }, { status: 500 });
    }

    return NextResponse.json({ category }, { status: 201 });
  } catch (error) {
    console.error('[CATEGORIES_CREATE_ERROR]', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
