import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useRouter } from 'next/navigation';
import UserProfile from '../UserProfile';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

// Mock useAuth from AuthProvider
jest.mock('../AuthProvider', () => ({
  useAuth: jest.fn(),
}));

// Mock useProfile hook
jest.mock('@/lib/hooks/useProfile', () => ({
  useProfile: jest.fn(),
}));

// Mock useAuth hook
jest.mock('@/lib/hooks/useAuth', () => ({
  useAuth: jest.fn(),
}));

import { useAuth } from '../AuthProvider';
import { useProfile } from '@/lib/hooks/useProfile';
import { useAuth as useAuthHook } from '@/lib/hooks/useAuth';

const mockPush = jest.fn();
const mockedUseAuth = useAuth as jest.Mock;
const mockedUseProfile = useProfile as jest.Mock;
const mockedUseAuthHook = useAuthHook as jest.Mock;
const mockedUseRouter = useRouter as jest.Mock;

type ProfileData = {
  id: string;
  full_name: string | null;
};

describe('UserProfile', () => {
  const user = { email: '<EMAIL>', id: '123' };

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();

    // Setup router mock
    mockedUseRouter.mockReturnValue({
      push: mockPush,
    });

    // Setup default useAuth mock
    mockedUseAuth.mockReturnValue({ user });

    // Setup default useAuthHook mock
    mockedUseAuthHook.mockReturnValue({
      logout: jest.fn(),
      isLoading: false,
      error: null,
    });
  });

  it('shows loading state while profile is loading', async () => {
    mockedUseProfile.mockReturnValue({
      profile: null,
      isLoading: true,
      error: null,
      updateProfile: jest.fn(),
      updateLoading: false,
      updateError: null,
      updateSuccess: null,
    });

    render(<UserProfile />);
    // Should show loading immediately
    expect(screen.getByText('Loading profile...')).toBeInTheDocument();
  });

  it('displays user email and full name after successful fetch', async () => {
    const mockProfile: ProfileData = { id: user.id, full_name: 'Test User' };
    mockedUseProfile.mockReturnValue({
      profile: mockProfile,
      isLoading: false,
      error: null,
      updateProfile: jest.fn(),
      updateLoading: false,
      updateError: null,
      updateSuccess: null,
    });

    render(<UserProfile />);
    // Wait for full name to appear
    expect(await screen.findByDisplayValue('Test User')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByLabelText('Full Name')).toBeInTheDocument();
  });

  it('shows empty input if user has no full_name', async () => {
    const mockProfile: ProfileData = { id: user.id, full_name: null };
    mockedUseProfile.mockReturnValue({
      profile: mockProfile,
      isLoading: false,
      error: null,
      updateProfile: jest.fn(),
      updateLoading: false,
      updateError: null,
      updateSuccess: null,
    });

    render(<UserProfile />);
    const input = await screen.findByLabelText('Full Name');
    expect(input).toHaveValue('');
  });

  it('shows error message if fetching profile fails', async () => {
    mockedUseProfile.mockReturnValue({
      profile: null,
      isLoading: false,
      error: 'Failed to load profile: Network error',
      updateProfile: jest.fn(),
      updateLoading: false,
      updateError: null,
      updateSuccess: null,
    });

    render(<UserProfile />);
    expect(await screen.findByRole('alert')).toHaveTextContent(
      'Failed to load profile: Network error'
    );
  });

  it('allows user to update full name via form', async () => {
    const mockUpdateProfile = jest.fn().mockResolvedValue(undefined);
    const mockProfile: ProfileData = { id: user.id, full_name: '' };

    mockedUseProfile.mockReturnValue({
      profile: mockProfile,
      isLoading: false,
      error: null,
      updateProfile: mockUpdateProfile,
      updateLoading: false,
      updateError: null,
      updateSuccess: 'Full name updated successfully.',
    });

    render(<UserProfile />);
    const input = await screen.findByLabelText('Full Name');
    expect(input).toHaveValue('');

    // Type new name
    await userEvent.type(input, 'New Name');
    // Submit form
    const button = screen.getByRole('button', { name: /update full name/i });
    expect(button).not.toBeDisabled();
    await userEvent.click(button);

    // Verify updateProfile was called
    expect(mockUpdateProfile).toHaveBeenCalledWith('New Name');

    // Wait for success message
    expect(await screen.findByText('Full name updated successfully.')).toBeInTheDocument();
  });

  it('shows error if updating full name fails', async () => {
    const mockUpdateProfile = jest.fn().mockResolvedValue(undefined);
    const mockProfile: ProfileData = { id: user.id, full_name: '' };

    mockedUseProfile.mockReturnValue({
      profile: mockProfile,
      isLoading: false,
      error: null,
      updateProfile: mockUpdateProfile,
      updateLoading: false,
      updateError: 'Failed to update full name: Update failed',
      updateSuccess: null,
    });

    render(<UserProfile />);
    const input = await screen.findByLabelText('Full Name');
    await userEvent.type(input, 'Fail Name');
    const button = screen.getByRole('button', { name: /update full name/i });
    await userEvent.click(button);

    expect(await screen.findByRole('alert')).toHaveTextContent(
      'Failed to update full name: Update failed'
    );
  });

  it('handles logout functionality', async () => {
    const mockLogout = jest.fn().mockResolvedValue(undefined);
    const mockProfile: ProfileData = { id: user.id, full_name: 'Test User' };

    mockedUseProfile.mockReturnValue({
      profile: mockProfile,
      isLoading: false,
      error: null,
      updateProfile: jest.fn(),
      updateLoading: false,
      updateError: null,
      updateSuccess: null,
    });

    mockedUseAuthHook.mockReturnValue({
      logout: mockLogout,
      isLoading: false,
      error: null,
    });

    render(<UserProfile />);

    const logoutButton = screen.getByRole('button', { name: /logout/i });
    await userEvent.click(logoutButton);

    expect(mockLogout).toHaveBeenCalled();
  });

  it('shows logout loading state', async () => {
    const mockProfile: ProfileData = { id: user.id, full_name: 'Test User' };

    mockedUseProfile.mockReturnValue({
      profile: mockProfile,
      isLoading: false,
      error: null,
      updateProfile: jest.fn(),
      updateLoading: false,
      updateError: null,
      updateSuccess: null,
    });

    mockedUseAuthHook.mockReturnValue({
      logout: jest.fn(),
      isLoading: true,
      error: null,
    });

    render(<UserProfile />);

    expect(screen.getByRole('button', { name: /logging out.../i })).toBeInTheDocument();
  });

  it('shows logout error', async () => {
    const mockProfile: ProfileData = { id: user.id, full_name: 'Test User' };
    const logoutError = { message: 'Logout failed' };

    mockedUseProfile.mockReturnValue({
      profile: mockProfile,
      isLoading: false,
      error: null,
      updateProfile: jest.fn(),
      updateLoading: false,
      updateError: null,
      updateSuccess: null,
    });

    mockedUseAuthHook.mockReturnValue({
      logout: jest.fn(),
      isLoading: false,
      error: logoutError,
    });

    render(<UserProfile />);

    expect(screen.getByText('Logout failed')).toBeInTheDocument();
  });

  it('shows "Loading user..." if user is null', () => {
    mockedUseAuth.mockReturnValue({ user: null });

    mockedUseProfile.mockReturnValue({
      profile: null,
      isLoading: false,
      error: null,
      updateProfile: jest.fn(),
      updateLoading: false,
      updateError: null,
      updateSuccess: null,
    });

    render(<UserProfile />);
    expect(screen.getByText('Loading user...')).toBeInTheDocument();
  });
});
