import { POST } from '../create/route';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest } from 'next/server';

// Mock dependencies
jest.mock('@/lib/supabase/server', () => ({
  createSupabaseServerClient: jest.fn(),
}));

jest.mock('next/server', () => ({
  ...jest.requireActual('next/server'),
  NextResponse: {
    json: jest.fn((body, init) => ({
      status: init?.status || 200,
      json: () => Promise.resolve(body),
    })),
  },
}));

describe('POST /api/rules/create', () => {
  const mockUser = { id: '550e8400-e29b-41d4-a716-446655440000' };
  const mockRuleData = {
    rule_type: 'merchant_name',
    match_criteria: 'Amazon',
    category_id: '550e8400-e29b-41d4-a716-************', // Valid <PERSON>
  };

  const createMockRequest = (body: Record<string, unknown>) => {
    return new Request('http://localhost/api/rules/create', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    }) as NextRequest;
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('Success (201 Created) creates rule with valid category_id', async () => {
    const mockCreatedRule = {
      id: '550e8400-e29b-41d4-a716-446655440002',
      user_id: mockUser.id,
      rule_type: 'merchant_name',
      match_criteria: 'Amazon',
      category_id: '550e8400-e29b-41d4-a716-************',
      created_at: '2025-01-01T00:00:00Z',
      updated_at: '2025-01-01T00:00:00Z',
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ data: mockCreatedRule, error: null }),
          }),
        }),
      }),
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(mockRuleData);
    const res = await POST(req);
    const body = await res.json();

    expect(res.status).toBe(201);
    expect(body).toEqual({ rule: mockCreatedRule });
    expect(mockSupabase.auth.getUser).toHaveBeenCalledTimes(1);
    expect(mockSupabase.from).toHaveBeenCalledWith('category_rules');
    expect(mockSupabase.from().insert).toHaveBeenCalledWith({
      user_id: mockUser.id,
      rule_type: 'merchant_name',
      match_criteria: 'Amazon',
      category_id: '550e8400-e29b-41d4-a716-************',
    });
  });

  test('Bad Request (400) returns error with invalid category_id format', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const invalidRuleData = {
      rule_type: 'merchant_name',
      match_criteria: 'Amazon',
      category_id: 'invalid-uuid', // Invalid UUID format
    };

    const req = createMockRequest(invalidRuleData);
    const res = await POST(req);
    const body = await res.json();

    expect(res.status).toBe(400);
    expect(body.error).toBeDefined();
  });

  test('Unauthorized (401) returns error when no user', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: null }, error: null }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(mockRuleData);
    const res = await POST(req);
    const body = await res.json();

    expect(res.status).toBe(401);
    expect(body.error).toBe('Unauthorized');
  });

  test('Database error (500) returns error when insert fails', async () => {
    const dbError = { message: 'Foreign key constraint violation', code: '23503' };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ data: null, error: dbError }),
          }),
        }),
      }),
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(mockRuleData);
    const res = await POST(req);
    const body = await res.json();

    expect(res.status).toBe(500);
    expect(body.error).toBe('Failed to create rule');
  });
});
