'use client';

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import CategorySelector from '@/components/categories/CategorySelector';

interface Category {
  id: string;
  name: string;
}

const ruleFormSchema = z.object({
  rule_type: z.enum(['merchant_name', 'keyword']),
  match_criteria: z.string().min(1, 'Match criteria is required'),
  category_id: z.string().min(1, 'Please select a category'),
});

export type RuleFormData = z.infer<typeof ruleFormSchema>;

export type Rule = {
  id: string;
  user_id: string;
  rule_type: 'merchant_name' | 'keyword';
  match_criteria: string;
  category_id: string;
  created_at: string;
  updated_at: string;
};

interface RuleFormProps {
  editingRule: Rule | null;
  isSubmitting: boolean;
  onSave: (data: RuleFormData) => Promise<void>;
  onCancel: () => void;
  categories: Category[];
}

export default function RuleForm({
  editingRule,
  isSubmitting,
  onSave,
  onCancel,
  categories,
}: RuleFormProps) {
  const form = useForm<RuleFormData>({
    resolver: zodResolver(ruleFormSchema),
    defaultValues: {
      rule_type: editingRule?.rule_type || 'merchant_name',
      match_criteria: editingRule?.match_criteria || '',
      category_id: editingRule?.category_id || '',
    },
  });

  useEffect(() => {
    form.reset({
      rule_type: editingRule?.rule_type || 'merchant_name',
      match_criteria: editingRule?.match_criteria || '',
      category_id: editingRule?.category_id || '',
    });
  }, [editingRule, form]);

  const handleSubmit = async (data: RuleFormData) => {
    try {
      await onSave(data);
      if (!editingRule) {
        form.reset();
      }
    } catch {
      // Error is handled by the parent component
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{editingRule ? 'Edit Rule' : 'Create New Rule'}</CardTitle>
        <CardDescription>
          {editingRule
            ? 'Update the details of your categorization rule.'
            : 'Add a new rule to automatically categorize transactions.'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-4'>
            <FormField
              control={form.control}
              name='rule_type'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Rule Type</FormLabel>
                  <select
                    {...field}
                    className='block w-full rounded-md border border-gray-300 bg-white py-2 px-3 text-sm focus:border-indigo-500 focus:ring-indigo-500'
                  >
                    <option value='merchant_name'>Merchant Name</option>
                    <option value='keyword'>Keyword</option>
                  </select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='match_criteria'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Match Criteria</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., 'Amazon' or 'Coffee'" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='category_id'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category</FormLabel>
                  <FormControl>
                    <CategorySelector
                      selectedValue={field.value}
                      onValueChange={field.onChange}
                      categories={categories}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='flex gap-2'>
              <Button type='submit' disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <LoadingSpinner />
                    {editingRule ? 'Saving...' : 'Creating...'}
                  </>
                ) : (
                  'Save Rule'
                )}
              </Button>
              {editingRule && (
                <Button type='button' variant='outline' onClick={onCancel}>
                  Cancel
                </Button>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
