'use client';

import { useState, useEffect } from 'react';

export interface Category {
  id: string;
  name: string;
  description: string | null;
  color: string | null;
  icon: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CategoryFormData {
  name: string;
  description: string;
  color: string;
  icon: string;
}

export interface UseCategoriesReturn {
  categories: Category[];
  isLoading: boolean;
  isSubmitting: boolean;
  error: string | null;
  editingCategory: Category | null;
  fetchCategories: () => Promise<void>;
  createCategory: (data: CategoryFormData) => Promise<void>;
  updateCategory: (categoryId: string, data: CategoryFormData) => Promise<void>;
  deleteCategory: (category: Category) => Promise<void>;
  setEditingCategory: (category: Category | null) => void;
  clearError: () => void;
}

export function useCategories(): UseCategoriesReturn {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Fetch categories
  const fetchCategories = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/categories/get');

      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }

      const data = await response.json();
      setCategories(data.categories || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch categories');
    } finally {
      setIsLoading(false);
    }
  };

  // Create category
  const createCategory = async (data: CategoryFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);

      const response = await fetch('/api/categories/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create category');
      }

      await fetchCategories();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create category');
      throw err; // Re-throw to allow form handling
    } finally {
      setIsSubmitting(false);
    }
  };

  // Update category
  const updateCategory = async (categoryId: string, data: CategoryFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);

      const payload = { category_id: categoryId, ...data };

      const response = await fetch('/api/categories/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update category');
      }

      setEditingCategory(null);
      await fetchCategories();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update category');
      throw err; // Re-throw to allow form handling
    } finally {
      setIsSubmitting(false);
    }
  };

  // Delete category
  const deleteCategory = async (category: Category) => {
    if (!confirm(`Are you sure you want to delete "${category.name}"?`)) {
      return;
    }

    try {
      setError(null);
      const response = await fetch('/api/categories/delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ category_id: category.id }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete category');
      }

      await fetchCategories();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete category');
    }
  };

  // Clear error
  const clearError = () => {
    setError(null);
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  return {
    categories,
    isLoading,
    isSubmitting,
    error,
    editingCategory,
    fetchCategories,
    createCategory,
    updateCategory,
    deleteCategory,
    setEditingCategory,
    clearError,
  };
}
