import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { getTransactions } from '@/lib/services/transactionService';

export async function GET(request: NextRequest) {
  const supabase = await createSupabaseServerClient();

  // Check authentication
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const userId = user.id;

  // Parse query parameters for pagination and filters
  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get('page') || '1', 10);
  const pageSize = parseInt(searchParams.get('pageSize') || '20', 10);

  // Parse filter parameters
  const searchQuery = searchParams.get('searchQuery') || undefined;
  const categoryId = searchParams.get('categoryId') || undefined;
  const startDate = searchParams.get('startDate') || undefined;
  const endDate = searchParams.get('endDate') || undefined;

  // Validate pagination parameters
  if (page < 1 || pageSize < 1 || pageSize > 100) {
    return NextResponse.json(
      {
        error:
          'Invalid pagination parameters. Page must be >= 1 and pageSize must be between 1 and 100.',
      },
      { status: 400 }
    );
  }

  // Validate date parameters if provided
  if (startDate && isNaN(Date.parse(startDate))) {
    return NextResponse.json(
      { error: 'Invalid startDate format. Use YYYY-MM-DD format.' },
      { status: 400 }
    );
  }

  if (endDate && isNaN(Date.parse(endDate))) {
    return NextResponse.json(
      { error: 'Invalid endDate format. Use YYYY-MM-DD format.' },
      { status: 400 }
    );
  }

  try {
    const { transactions, totalCount } = await getTransactions(supabase, userId, page, pageSize, {
      searchQuery,
      categoryId,
      startDate,
      endDate,
    });

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / pageSize);

    return NextResponse.json({
      transactions,
      pagination: {
        currentPage: page,
        pageSize,
        totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    });
  } catch (error) {
    console.error('[TRANSACTIONS_GET_ERROR]', error);
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
