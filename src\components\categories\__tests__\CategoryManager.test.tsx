import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CategoryManager from '../CategoryManager';
import { useCategories } from '@/lib/hooks/useCategories';

// Mock the useCategories hook
jest.mock('@/lib/hooks/useCategories');

// Mock UI components with simple implementations
jest.mock('@/components/ui/LoadingSpinner', () => ({
  __esModule: true,
  LoadingSpinner: () => <div data-testid='loading-spinner'>Loading...</div>,
}));

jest.mock('@/components/ui/button', () => ({
  __esModule: true,
  Button: ({ children, onClick, disabled, type, variant, size, ...props }: any) => (
    <button
      onClick={onClick}
      disabled={disabled}
      type={type}
      data-variant={variant}
      data-size={size}
      {...props}
    >
      {children}
    </button>
  ),
}));

jest.mock('@/components/ui/input', () => ({
  __esModule: true,
  Input: React.forwardRef<HTMLInputElement, any>((props, ref) => <input ref={ref} {...props} />),
}));

jest.mock('@/components/ui/card', () => ({
  __esModule: true,
  Card: ({ children, ...props }: any) => (
    <div data-testid='card' {...props}>
      {children}
    </div>
  ),
  CardContent: ({ children, ...props }: any) => (
    <div data-testid='card-content' {...props}>
      {children}
    </div>
  ),
  CardDescription: ({ children, ...props }: any) => (
    <div data-testid='card-description' {...props}>
      {children}
    </div>
  ),
  CardHeader: ({ children, ...props }: any) => (
    <div data-testid='card-header' {...props}>
      {children}
    </div>
  ),
  CardTitle: ({ children, ...props }: any) => (
    <div data-testid='card-title' {...props}>
      {children}
    </div>
  ),
}));

jest.mock('@/components/ui/form', () => ({
  __esModule: true,
  Form: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  FormControl: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  FormField: ({ render, name }: any) => {
    const field = {
      value: '',
      onChange: jest.fn(),
      onBlur: jest.fn(),
      name,
    };
    return render({ field });
  },
  FormItem: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  FormLabel: ({ children, ...props }: any) => <label {...props}>{children}</label>,
  FormMessage: ({ children, ...props }: any) => <div {...props}>{children}</div>,
}));

// Mock react-hook-form
const mockReset = jest.fn();
const mockHandleSubmit = jest.fn((fn: (data: any) => void) => (e: React.FormEvent) => {
  e.preventDefault();
  const formData = {
    name: 'Test Category',
    description: 'Test Description',
    color: '#ef4444',
    icon: '🍔',
  };
  fn(formData);
});

jest.mock('react-hook-form', () => ({
  useForm: () => ({
    control: {},
    handleSubmit: mockHandleSubmit,
    reset: mockReset,
    formState: { errors: {} },
    getValues: jest.fn(),
    setValue: jest.fn(),
    watch: jest.fn(),
  }),
  FormProvider: ({ children, ...props }: { children: React.ReactNode; [key: string]: any }) => {
    // Filter out react-hook-form methods that shouldn't be passed to DOM
    const { handleSubmit, reset, formState, getValues, setValue, watch, control, ...cleanProps } =
      props;
    return <div {...cleanProps}>{children}</div>;
  },
  Controller: ({ render }: { render: any }) =>
    render({ field: { onChange: jest.fn(), value: '' } }),
}));

// Mock the Form components
jest.mock('@/components/ui/form', () => ({
  Form: ({ children, ...props }: { children: React.ReactNode; [key: string]: any }) => {
    // Filter out react-hook-form methods that shouldn't be passed to DOM
    const { handleSubmit, reset, formState, getValues, setValue, watch, control, ...cleanProps } =
      props;
    return <div {...cleanProps}>{children}</div>;
  },
  FormField: ({ render }: { render: any }) => render({ field: { onChange: jest.fn(), value: '' } }),
  FormItem: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  FormLabel: ({ children }: { children: React.ReactNode }) => <label>{children}</label>,
  FormControl: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  FormMessage: () => <div />,
}));

describe('CategoryManager Component', () => {
  const mockCategories = [
    {
      id: 'cat-1',
      name: 'Food & Dining',
      description: 'Restaurant and food purchases',
      color: '#ef4444',
      icon: '🍔',
      is_active: true,
      created_at: '2025-01-01T00:00:00Z',
      updated_at: '2025-01-02T00:00:00Z',
    },
    {
      id: 'cat-2',
      name: 'Transportation',
      description: 'Gas and car expenses',
      color: '#3b82f6',
      icon: '🚗',
      is_active: true,
      created_at: '2025-01-03T00:00:00Z',
      updated_at: '2025-01-04T00:00:00Z',
    },
  ];

  const mockUseCategories = {
    categories: mockCategories,
    isLoading: false,
    isSubmitting: false,
    error: null,
    editingCategory: null,
    fetchCategories: jest.fn(),
    createCategory: jest.fn(),
    updateCategory: jest.fn(),
    deleteCategory: jest.fn(),
    setEditingCategory: jest.fn(),
    clearError: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useCategories as jest.Mock).mockReturnValue(mockUseCategories);
  });

  // Test Case 1: Initial Load and Display
  describe('Initial Load and Display', () => {
    test('shows loading spinner when isLoading is true', () => {
      (useCategories as jest.Mock).mockReturnValue({
        ...mockUseCategories,
        isLoading: true,
      });

      render(<CategoryManager />);

      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });

    test('displays categories when loaded', () => {
      render(<CategoryManager />);

      expect(screen.getByText('Food & Dining')).toBeInTheDocument();
      expect(screen.getByText('Restaurant and food purchases')).toBeInTheDocument();
      expect(screen.getByText('Transportation')).toBeInTheDocument();
      expect(screen.getByText('Gas and car expenses')).toBeInTheDocument();
    });

    test('displays empty state when no categories exist', () => {
      (useCategories as jest.Mock).mockReturnValue({
        ...mockUseCategories,
        categories: [],
      });

      render(<CategoryManager />);

      expect(
        screen.getByText('No categories created yet. Create your first category above!')
      ).toBeInTheDocument();
    });

    test('displays error message when error exists', () => {
      (useCategories as jest.Mock).mockReturnValue({
        ...mockUseCategories,
        error: 'Failed to fetch categories',
      });

      render(<CategoryManager />);

      expect(screen.getByText('Failed to fetch categories')).toBeInTheDocument();
    });
  });

  // Test Case 2: Create a New Category
  describe('Create a New Category', () => {
    test('calls createCategory when form is submitted in create mode', async () => {
      const mockCreateCategory = jest.fn().mockResolvedValue(undefined);
      (useCategories as jest.Mock).mockReturnValue({
        ...mockUseCategories,
        createCategory: mockCreateCategory,
      });

      render(<CategoryManager />);

      const createButton = screen.getByRole('button', { name: /create category/i });
      fireEvent.click(createButton);

      await waitFor(() => {
        expect(mockCreateCategory).toHaveBeenCalledWith({
          name: 'Test Category',
          description: 'Test Description',
          color: '#ef4444',
          icon: '🍔',
        });
      });
    });

    test('shows submitting state when creating category', () => {
      (useCategories as jest.Mock).mockReturnValue({
        ...mockUseCategories,
        isSubmitting: true,
      });

      render(<CategoryManager />);

      expect(screen.getByText('Creating...')).toBeInTheDocument();
    });
  });

  // Test Case 3: Edit an Existing Category
  describe('Edit an Existing Category', () => {
    test('shows edit mode when editingCategory is set', () => {
      (useCategories as jest.Mock).mockReturnValue({
        ...mockUseCategories,
        editingCategory: mockCategories[0],
      });

      render(<CategoryManager />);

      expect(screen.getByText('Edit Category')).toBeInTheDocument();
      expect(screen.getByText('Update the details of your category')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /update category/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
    });

    test('calls updateCategory when form is submitted in edit mode', async () => {
      const mockUpdateCategory = jest.fn().mockResolvedValue(undefined);
      (useCategories as jest.Mock).mockReturnValue({
        ...mockUseCategories,
        editingCategory: mockCategories[0],
        updateCategory: mockUpdateCategory,
      });

      render(<CategoryManager />);

      const updateButton = screen.getByRole('button', { name: /update category/i });
      fireEvent.click(updateButton);

      await waitFor(() => {
        expect(mockUpdateCategory).toHaveBeenCalledWith('cat-1', {
          name: 'Test Category',
          description: 'Test Description',
          color: '#ef4444',
          icon: '🍔',
        });
      });
    });

    test('calls setEditingCategory with null when cancel is clicked', () => {
      const mockSetEditingCategory = jest.fn();
      (useCategories as jest.Mock).mockReturnValue({
        ...mockUseCategories,
        editingCategory: mockCategories[0],
        setEditingCategory: mockSetEditingCategory,
      });

      render(<CategoryManager />);

      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      fireEvent.click(cancelButton);

      expect(mockSetEditingCategory).toHaveBeenCalledWith(null);
    });
  });

  // Test Case 4: Delete a Category
  describe('Delete a Category', () => {
    test('calls deleteCategory when delete button is clicked', () => {
      const mockDeleteCategory = jest.fn();
      (useCategories as jest.Mock).mockReturnValue({
        ...mockUseCategories,
        deleteCategory: mockDeleteCategory,
      });

      render(<CategoryManager />);

      const deleteButtons = screen.getAllByText('Delete');
      fireEvent.click(deleteButtons[0]);

      expect(mockDeleteCategory).toHaveBeenCalledWith(mockCategories[0]);
    });
  });

  // Test Case 5: Edit Button Functionality
  describe('Edit Button Functionality', () => {
    test('calls setEditingCategory when edit button is clicked', () => {
      const mockSetEditingCategory = jest.fn();
      (useCategories as jest.Mock).mockReturnValue({
        ...mockUseCategories,
        setEditingCategory: mockSetEditingCategory,
      });

      render(<CategoryManager />);

      const editButtons = screen.getAllByText('Edit');
      fireEvent.click(editButtons[0]);

      expect(mockSetEditingCategory).toHaveBeenCalledWith(mockCategories[0]);
    });
  });

  // Test Case 6: Component Integration
  describe('Component Integration', () => {
    test('renders CategoryForm and CategoryList components', () => {
      render(<CategoryManager />);

      // Check for form elements
      expect(screen.getByText('Create New Category')).toBeInTheDocument();
      expect(screen.getByText('Add a new custom spending category')).toBeInTheDocument();

      // Check for list elements
      expect(screen.getByText('Your Categories')).toBeInTheDocument();
      expect(screen.getByText('Manage your custom spending categories')).toBeInTheDocument();
    });
  });
});
