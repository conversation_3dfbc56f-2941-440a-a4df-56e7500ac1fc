import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { createSupabaseServerClient } from '@/lib/supabase/server';

const updateCategorySchema = z
  .object({
    category_id: z.string(),
    name: z.string().trim().min(1).optional(),
    color: z.string().nullable().optional(),
    icon: z.string().nullable().optional(),
    description: z.string().nullable().optional(),
  })
  .refine(
    (data) => {
      const updateFieldKeys = Object.keys(data).filter((key) => key !== 'category_id');
      return updateFieldKeys.length > 0;
    },
    {
      message: 'At least one field must be provided for update',
      path: [],
    }
  );

function createUpdatePayload(
  validatedData: Omit<z.infer<typeof updateCategorySchema>, 'category_id'>
): Record<string, unknown> {
  const payload: Record<string, unknown> = {};
  for (const key in validatedData) {
    if (validatedData[key as keyof typeof validatedData] !== undefined) {
      payload[key] = validatedData[key as keyof typeof validatedData];
    }
  }
  return payload;
}

export async function PUT(request: NextRequest) {
  const supabase = await createSupabaseServerClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const body = await request.json();
    const validation = updateCategorySchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json({ error: validation.error.flatten() }, { status: 400 });
    }

    const { category_id, ...updateFields } = validation.data;

    const { data: existingCategory, error: fetchError } = await supabase
      .from('user_categories')
      .select('id')
      .eq('id', category_id)
      .eq('user_id', user.id)
      .single();

    if (fetchError || !existingCategory) {
      return NextResponse.json({ error: 'Category not found or access denied' }, { status: 404 });
    }

    const updateData = createUpdatePayload(updateFields);

    const { data: updatedCategory, error: updateError } = await supabase
      .from('user_categories')
      .update(updateData)
      .eq('id', category_id)
      .eq('user_id', user.id)
      .select()
      .single();

    if (updateError) {
      if (updateError.code === '23505') {
        return NextResponse.json(
          { error: 'A category with this name already exists' },
          { status: 409 }
        );
      }
      console.error('Error updating category:', updateError);
      return NextResponse.json({ error: 'Failed to update category' }, { status: 500 });
    }

    return NextResponse.json({ category: updatedCategory });
  } catch (error) {
    console.error('[CATEGORIES_UPDATE_ERROR]', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
