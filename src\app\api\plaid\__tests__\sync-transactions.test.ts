import { POST } from '../sync-transactions/route';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import {
  getFinancialAccountCredentials,
  fetchPlaidTransactions,
  handleRemovedTransactions,
  updateAccountSyncMetadata,
} from '@/lib/services/transactionService';
import { NextRequest } from 'next/server';
import { RemovedTransaction, Transaction } from 'plaid';

// Mock dependencies
jest.mock('@/lib/supabase/server', () => ({
  createSupabaseServerClient: jest.fn(),
}));

jest.mock('@/lib/services/transactionService', () => ({
  getFinancialAccountCredentials: jest.fn(),
  fetchPlaidTransactions: jest.fn(),
  handleRemovedTransactions: jest.fn(),
  updateAccountSyncMetadata: jest.fn(),
}));

// Mock next/server
jest.mock('next/server', () => ({
  ...jest.requireActual('next/server'),
  NextResponse: {
    json: jest.fn((body, init) => ({
      status: init?.status || 200,
      json: () => Promise.resolve(body),
    })),
  },
}));

describe('POST /api/plaid/sync-transactions', () => {
  const mockUser = { id: 'test-user-id' };
  const mockItemId = 'test-item-id';
  const mockAccessToken = 'test-access-token';
  const mockCursor = 'test-cursor';
  const mockAccountId = 'test-account-id';

  interface MockRequestBody {
    item_id: string;
  }

  const mockCredentials = {
    accountId: mockAccountId,
    accessToken: mockAccessToken,
    cursor: mockCursor,
  };

  const mockPlaidResponse = {
    added: [
      {
        transaction_id: 'added-1',
        amount: 10,
        name: 'New Purchase',
        category: ['Food and Drink', 'Restaurants'],
        date: '2025-06-08',
      } as Transaction,
    ],
    modified: [
      {
        transaction_id: 'modified-1',
        amount: 20,
        name: 'Updated Purchase',
        category: ['Travel', 'Airlines'],
        date: '2025-06-08',
      } as Transaction,
    ],
    removed: [{ transaction_id: 'removed-1' } as RemovedTransaction],
    nextCursor: 'new-cursor',
  };

  const mockAccountData = {
    plaid_metadata: {
      access_token: mockAccessToken,
      cursor: mockCursor,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const createMockRequest = (body: MockRequestBody) => {
    return new Request('http://localhost/api/plaid/sync-transactions', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    }) as NextRequest;
  };

  const createMockSupabase = (accountData = mockAccountData) => ({
    auth: {
      getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
    },
    from: jest.fn().mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({ data: accountData, error: null }),
    }),
    rpc: jest.fn().mockResolvedValue({}),
  });

  test('Success (200 OK) with new, updated, and removed transactions', async () => {
    const mockSupabase = createMockSupabase();
    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);
    (getFinancialAccountCredentials as jest.Mock).mockResolvedValue(mockCredentials);
    (fetchPlaidTransactions as jest.Mock).mockResolvedValue(mockPlaidResponse);
    (mockSupabase.rpc as jest.Mock).mockResolvedValue({
      data: { added: 1, modified: 1 },
      error: null,
    });
    (handleRemovedTransactions as jest.Mock).mockResolvedValue(undefined);
    (updateAccountSyncMetadata as jest.Mock).mockResolvedValue(undefined);

    const req = createMockRequest({ item_id: mockItemId });
    const res = await POST(req);

    expect(res.status).toBe(200);
    const body = await res.json();
    expect(body.summary).toEqual({
      added: 1,
      modified: 1,
      removed: 1,
      next_cursor: 'new-cursor',
    });

    // Verify service functions were called correctly
    expect(getFinancialAccountCredentials).toHaveBeenCalledWith(
      mockSupabase,
      mockUser.id,
      mockItemId
    );
    expect(fetchPlaidTransactions).toHaveBeenCalledWith(mockAccessToken, mockCursor);
    expect(mockSupabase.rpc).toHaveBeenCalledWith('handle_transaction_sync', {
      p_user_id: mockUser.id,
      p_account_id: mockAccountId,
      p_added_transactions: mockPlaidResponse.added,
      p_modified_transactions: mockPlaidResponse.modified,
    });
    expect(handleRemovedTransactions).toHaveBeenCalledWith(
      mockSupabase,
      mockPlaidResponse.removed,
      mockUser.id
    );
    expect(updateAccountSyncMetadata).toHaveBeenCalledWith(
      mockSupabase,
      mockAccountId,
      mockAccountData.plaid_metadata,
      'new-cursor'
    );
  });

  test('Success (200 OK) with no transactions to process', async () => {
    const emptyPlaidResponse = {
      added: [],
      modified: [],
      removed: [],
      nextCursor: 'new-cursor',
    };

    const mockSupabase = createMockSupabase();
    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);
    (getFinancialAccountCredentials as jest.Mock).mockResolvedValue(mockCredentials);
    (fetchPlaidTransactions as jest.Mock).mockResolvedValue(emptyPlaidResponse);
    (updateAccountSyncMetadata as jest.Mock).mockResolvedValue(undefined);
    (mockSupabase.rpc as jest.Mock).mockResolvedValue({
      data: { added: 0, modified: 0 },
      error: null,
    });

    const req = createMockRequest({ item_id: mockItemId });
    const res = await POST(req);

    expect(res.status).toBe(200);
    const body = await res.json();
    expect(body.summary).toEqual({
      added: 0,
      modified: 0,
      removed: 0,
      next_cursor: 'new-cursor',
    });

    // Verify RPC is called and handleRemovedTransactions is not
    expect(mockSupabase.rpc).toHaveBeenCalledWith('handle_transaction_sync', {
      p_user_id: mockUser.id,
      p_account_id: mockAccountId,
      p_added_transactions: [],
      p_modified_transactions: [],
    });
    expect(handleRemovedTransactions).not.toHaveBeenCalled();
    expect(updateAccountSyncMetadata).toHaveBeenCalled();
  });

  test('Missing item_id (400 Bad Request)', async () => {
    const req = createMockRequest({ item_id: '' });
    const res = await POST(req);

    expect(res.status).toBe(400);
    expect(await res.json()).toEqual({ error: 'Missing required field: item_id' });
  });

  test('Unauthorized (401)', async () => {
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest
          .fn()
          .mockResolvedValue({ data: { user: null }, error: { message: 'Unauthorized' } }),
      },
    });

    const req = createMockRequest({ item_id: mockItemId });
    const res = await POST(req);

    expect(res.status).toBe(401);
    expect(await res.json()).toEqual({ error: 'Unauthorized' });
  });

  test('Account Not Found (404)', async () => {
    const mockSupabase = createMockSupabase();
    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);
    (getFinancialAccountCredentials as jest.Mock).mockRejectedValue(
      new Error('Financial account not found for this item_id')
    );

    const req = createMockRequest({ item_id: mockItemId });
    const res = await POST(req);

    expect(res.status).toBe(404);
    expect(await res.json()).toEqual({ error: 'Financial account not found for this item_id' });
  });

  test('Access Token Not Found (500)', async () => {
    const mockSupabase = createMockSupabase();
    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);
    (getFinancialAccountCredentials as jest.Mock).mockRejectedValue(
      new Error('Access token not found in account metadata')
    );

    const req = createMockRequest({ item_id: mockItemId });
    const res = await POST(req);

    expect(res.status).toBe(500);
    expect(await res.json()).toEqual({ error: 'Access token not found in account metadata' });
  });

  test('Plaid Client Error (500)', async () => {
    const mockSupabase = createMockSupabase();
    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);
    (getFinancialAccountCredentials as jest.Mock).mockResolvedValue(mockCredentials);
    (fetchPlaidTransactions as jest.Mock).mockRejectedValue(new Error('Plaid API error'));

    const req = createMockRequest({ item_id: mockItemId });
    const res = await POST(req);

    expect(res.status).toBe(500);
    expect(await res.json()).toEqual({ error: 'Internal Server Error' });
  });

  test('Database Error during Transaction Upsert (500)', async () => {
    const mockSupabase = createMockSupabase();
    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);
    (getFinancialAccountCredentials as jest.Mock).mockResolvedValue(mockCredentials);
    (fetchPlaidTransactions as jest.Mock).mockResolvedValue(mockPlaidResponse);
    (mockSupabase.rpc as jest.Mock).mockRejectedValue(
      new Error('Failed to upsert transaction: DB upsert failed')
    );

    const req = createMockRequest({ item_id: mockItemId });
    const res = await POST(req);

    expect(res.status).toBe(500);
    expect(await res.json()).toEqual({ error: 'Failed to upsert transaction: DB upsert failed' });
  });

  test('Database Error during Removed Transaction Handling (500)', async () => {
    const mockSupabase = createMockSupabase();
    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);
    (getFinancialAccountCredentials as jest.Mock).mockResolvedValue(mockCredentials);
    (fetchPlaidTransactions as jest.Mock).mockResolvedValue(mockPlaidResponse);
    (handleRemovedTransactions as jest.Mock).mockRejectedValue(
      new Error('Failed to mark transaction as removed: DB update failed')
    );

    const req = createMockRequest({ item_id: mockItemId });
    const res = await POST(req);

    expect(res.status).toBe(500);
    expect(await res.json()).toEqual({
      error: 'Failed to mark transaction as removed: DB update failed',
    });
  });

  test('Database Error during Cursor Update (500)', async () => {
    const mockSupabase = createMockSupabase();
    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);
    (getFinancialAccountCredentials as jest.Mock).mockResolvedValue(mockCredentials);
    (fetchPlaidTransactions as jest.Mock).mockResolvedValue(mockPlaidResponse);
    (handleRemovedTransactions as jest.Mock).mockResolvedValue(undefined);
    (updateAccountSyncMetadata as jest.Mock).mockRejectedValue(
      new Error('Failed to update sync cursor: DB update failed')
    );

    const req = createMockRequest({ item_id: mockItemId });
    const res = await POST(req);

    expect(res.status).toBe(500);
    expect(await res.json()).toEqual({ error: 'Failed to update sync cursor: DB update failed' });
  });
});
