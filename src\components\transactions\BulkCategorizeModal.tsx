'use client';

import React from 'react';
import { toast } from 'sonner';
import CategorySelector from '@/components/categories/CategorySelector';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface BulkCategorizeModalProps {
  transactionIds: string[];
  onClose: () => void;
  onSuccess: () => void;
}

export default function BulkCategorizeModal({
  transactionIds,
  onClose,
  onSuccess,
}: BulkCategorizeModalProps) {
  const [selectedCategoryId, setSelectedCategoryId] = React.useState('');
  const [isUpdating, setIsUpdating] = React.useState(false);

  const handleConfirm = async () => {
    if (!selectedCategoryId) {
      toast.error('Please select a category.');
      return;
    }

    setIsUpdating(true);
    try {
      const response = await fetch('/api/transactions/bulk-update-category', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactionIds,
          categoryId: selectedCategoryId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update categories.');
      }

      toast.success('Transactions categorized successfully!');
      onSuccess();
    } catch (error) {
      const err = error as Error;
      console.error('An error occurred while categorizing transactions:', err);
      toast.error(`An error occurred: ${err.message}`);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className='fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50'>
      <div className='bg-white rounded-lg shadow-xl p-6 w-full max-w-md'>
        <h2 className='text-lg font-semibold mb-4'>
          Categorize {transactionIds.length} Transactions
        </h2>
        <div className='space-y-4'>
          <div>
            <label
              htmlFor='bulk-category-selector'
              className='block text-sm font-medium text-gray-700 mb-1'
            >
              Select New Category
            </label>
            <CategorySelector
              selectedValue={selectedCategoryId}
              onValueChange={setSelectedCategoryId}
            />
          </div>
          <div className='flex justify-end space-x-3'>
            <button
              onClick={onClose}
              disabled={isUpdating}
              className='px-4 py-2 text-sm bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 disabled:opacity-50'
            >
              Cancel
            </button>
            <button
              onClick={handleConfirm}
              disabled={isUpdating || !selectedCategoryId}
              className='px-4 py-2 text-sm bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 flex items-center'
            >
              {isUpdating && <LoadingSpinner className='mr-2 h-4 w-4' />}
              Confirm
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
