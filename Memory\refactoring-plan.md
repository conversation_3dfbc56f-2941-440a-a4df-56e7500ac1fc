# Refactoring and Documentation Update Plan

This document outlines a detailed, actionable, step-by-step plan for refactoring the NAVsync codebase and updating the `DEVELOPMENT.md` file.

## Part 1: Code Refactoring Plan

### Subtask 1.1: Refactor `categories/create` Route

- **Title:** Refactor Category Creation Route to Use Schema-Based Validation
- **File(s) to Modify:** `src/app/api/categories/create/route.ts`
- **Reason:** The route handler contains verbose, manual validation for multiple fields, making it complex and hard to maintain.
- **Instructions:**
  1.  Create a new Zod schema for category creation that defines the expected shape and validation rules for `name`, `type`, `icon`, and `color`.
  2.  In the route handler, replace the manual validation checks with a call to the Zod schema's `parse` method.
  3.  Update the error handling to catch and respond to Zod validation errors.
- **Mode:** `Junior`

### Subtask 1.2: Refactor `categories/delete` Route

- **Title:** Extract Category Deletion Business Logic into a Service
- **File(s) to Modify:** `src/app/api/categories/delete/route.ts`
- **Reason:** The function mixes authorization, validation, dependency checking, and deletion logic, violating the Single Responsibility Principle.
- **Instructions:**
  1.  Create a new service function `deleteCategory` in a new file `src/lib/services/categoryService.ts`.
  2.  Move the logic for checking if a category is in use by any transactions from the route handler to the `deleteCategory` function.
  3.  The `deleteCategory` function should accept the `categoryId` and `userId` as arguments.
  4.  Update the route handler to call the `deleteCategory` service function.
- **Mode:** `Midlevel`

### Subtask 1.3: Refactor `categories/update` Route

- **Title:** Simplify Category Update Route by Extracting Logic
- **File(s) to Modify:** `src/app/api/categories/update/route.ts`
- **Reason:** The route has complex logic for validating optional fields and dynamically building the update payload.
- **Instructions:**
  1.  Create a Zod schema for category updates with all fields marked as optional.
  2.  Create a utility function that takes the validated request body and constructs the payload for the Supabase update query, filtering out any undefined fields.
  3.  Update the route handler to use the new schema for validation and the utility function for payload construction.
- **Mode:** `Midlevel`

### Subtask 1.4: Refactor `plaid/exchange-public-token` Route

- **Title:** Separate Plaid API and Database Logic in Token Exchange
- **File(s) to Modify:** `src/app/api/plaid/exchange-public-token/route.ts`
- **Reason:** The route violates SRP by handling both the Plaid API token exchange and direct database insertions.
- **Instructions:**
  1.  Create a new data service function `createFinancialAccount` in a new file `src/lib/services/plaidService.ts`.
  2.  Move the database insertion logic for creating the financial account record from the route handler to the `createFinancialAccount` function.
  3.  The route handler should now call the Plaid API, and then pass the necessary data to the `createFinancialAccount` function.
- **Mode:** `Midlevel`

### Subtask 1.5: Refactor `plaid/sync-transactions` Route

- **Title:** Decompose `sync-transactions` "God" Function
- **File(s) to Modify:** `src/app/api/plaid/sync-transactions/route.ts`
- **Reason:** This large function violates SRP by handling credential fetching, Plaid API calls, and complex data processing for added, modified, and removed transactions.
- **Instructions:**
  1.  Create a new file `src/lib/services/transactionService.ts`.
  2.  Create a function `getFinancialAccountCredentials` to fetch the access token.
  3.  Create a function `fetchPlaidTransactions` to handle the Plaid API call.
  4.  Create a function `upsertTransactions` to handle the logic for adding and updating transactions in the database.
  5.  Create a function `handleRemovedTransactions` to process removed transactions.
  6.  Refactor the main route handler to be a simple orchestrator that calls these new functions in sequence.
- **Mode:** `Senior`

### Subtask 1.6: Refactor `transactions/categorize` Route

- **Title:** Abstract Ownership Verification in Transaction Categorization
- **File(s) to Modify:** `src/app/api/transactions/categorize/route.ts`
- **Reason:** The route handler contains duplicated logic for verifying transaction and category ownership.
- **Instructions:**
  1.  Create a reusable function `verifyOwnership` in `src/lib/utils.ts` that takes a `userId` and a table name and record ID to check for ownership.
  2.  Update the route handler to use this new function for both transaction and category ownership verification.
- **Mode:** `Junior`

### Subtask 1.7: Refactor `transactions/get` Route

- **Title:** Abstract Data Access and Pagination in `get` Transactions Route
- **File(s) to Modify:** `src/app/api/transactions/get/route.ts`
- **Reason:** The file combines pagination logic, total count calculation, and data fetching with a complex Supabase query.
- **Instructions:**
  1.  In `src/lib/services/transactionService.ts`, create a function `getTransactions` that encapsulates the entire data fetching and pagination logic.
  2.  This function should accept pagination parameters (`page`, `pageSize`) and any filters as arguments.
  3.  The function should return the list of transactions and the total count.
  4.  Update the route handler to call this new service function and return its result.
- **Mode:** `Midlevel`

### Subtask 1.8: Refactor `LoginForm` Component

- **Title:** Extract Authentication Logic from `LoginForm`
- **File(s) to Modify:** `src/components/auth/LoginForm.tsx`
- **Reason:** The component is a "God" Component that mixes UI rendering with direct API calls for authentication.
- **Instructions:**
  1.  Create a new file `src/lib/hooks/useAuth.ts`.
  2.  Create a custom hook `useAuth` that exposes a `signIn` function.
  3.  Move the `supabase.auth.signInWithPassword` call from the component into the `signIn` function within the hook.
  4.  The hook should manage its own loading and error states.
  5.  Update the `LoginForm` component to use the `useAuth` hook.
- **Mode:** `Junior`

### Subtask 1.9: Refactor `SignUpForm` Component

- **Title:** Extract Sign-Up Logic from `SignUpForm`
- **File(s) to Modify:** `src/components/auth/SignUpForm.tsx`
- **Reason:** The component mixes UI, form state, and direct API calls for user sign-up.
- **Instructions:**
  1.  Add a `signUp` function to the `useAuth` hook created in the previous step.
  2.  Move the `supabase.auth.signUp` call from the component into the `signUp` function.
  3.  Update the `SignUpForm` component to use the `useAuth` hook.
- **Mode:** `Junior`

### Subtask 1.10: Refactor `UserProfile` Component

- **Title:** Decompose `UserProfile` "God" Component
- **File(s) to Modify:** `src/components/auth/UserProfile.tsx`
- **Reason:** This large component handles data fetching, form state, UI, and logout logic.
- **Instructions:**
  1.  Create a custom hook `useProfile` in a new file `src/lib/hooks/useProfile.ts`.
  2.  The `useProfile` hook should be responsible for fetching the user profile data and providing an `updateProfile` function. It should manage its own loading, error, and success states.
  3.  Add a `logout` function to the `useAuth` hook.
  4.  Refactor the `UserProfile` component to use the `useProfile` hook for data and updates, and the `useAuth` hook for the logout functionality.
- **Mode:** `Senior`

### Subtask 1.11: Refactor `CategoryManager` Component

- **Title:** Decompose `CategoryManager` "God" Component
- **File(s) to Modify:** `src/components/categories/CategoryManager.tsx`
- **Reason:** This massive component (404 lines) manages all state and logic for CRUD operations on categories, plus all the UI.
- **Instructions:**
  1.  Create a custom hook `useCategories` in a new file `src/lib/hooks/useCategories.ts` to manage all state and API interactions (fetching, creating, updating, deleting).
  2.  Create a `CategoryList` component that receives the list of categories from its parent and renders them.
  3.  Create a `CategoryListItem` component for rendering a single category with edit and delete buttons.
  4.  Create a `CategoryForm` component for creating and editing categories.
  5.  Update the `CategoryManager` to be a container component that uses the `useCategories` hook and orchestrates the `CategoryList` and `CategoryForm` components.
- **Mode:** `Senior`

### Subtask 1.12: Refactor `PlaidLink` Component

- **Title:** Extract PlaidLink Logic into a Custom Hook
- **File(s) to Modify:** `src/components/plaid/PlaidLink.tsx`
- **Reason:** The component mixes UI with data fetching for the link token and handling the `onSuccess` and `onExit` callbacks.
- **Instructions:**
  1.  Create a custom hook `usePlaidLink` in a new file `src/lib/hooks/usePlaidLink.ts`.
  2.  This hook should handle fetching the link token and contain the `onSuccess` and `onExit` callback logic.
  3.  The `PlaidLink` component should be refactored to be a more presentational component that uses the `usePlaidLink` hook to get the necessary props for the `PlaidLink` component from `react-plaid-link`.
- **Mode:** `Midlevel`

### Subtask 1.13: Refactor `TransactionsList` Component

- **Title:** Extract Transaction Fetching Logic into a Custom Hook
- **File(s) to Modify:** `src/components/transactions/TransactionsList.tsx`
- **Reason:** The component manages state for transactions, pagination, loading, and errors, and contains the data fetching logic.
- **Instructions:**
  1.  Create a custom hook `useTransactions` in a new file `src/lib/hooks/useTransactions.ts`.
  2.  This hook will manage all state related to transactions (data, pagination, loading, errors) and contain the logic for fetching the data.
  3.  Refactor the `TransactionsList` component to be a presentational component that uses the `useTransactions` hook.
- **Mode:** `Midlevel`

## Part 2: Documentation (`DEVELOPMENT.md`) Update Plan

### Subtask 2.1: Add "Quick Start" Section

- **Title:** Add "Quick Start" Section to `DEVELOPMENT.md`
- **File(s) to Modify:** `DEVELOPMENT.md`
- **Reason:** The document is missing a crucial "Quick Start" guide for new developers.
- **Instructions:**

  1.  Add a new `## Quick Start` section at the top of the file.
  2.  Include the following steps with code blocks:

      ````markdown
      ## Quick Start

      1.  **Clone the repository:**

          ```bash
          git clone https://github.com/your-repo/navsync.git
          cd navsync
          ```

      2.  **Install dependencies:**

          ```bash
          npm install
          ```

      3.  **Set up environment variables:**
          Copy the `.env.example` file to `.env.local` and fill in the required values. See the "Environment Variables" section for details.

          ```bash
          cp .env.example .env.local
          ```

      4.  **Run the development server:**
          ```bash
          npm run dev
          ```
      ````

- **Mode:** `Intern`

### Subtask 2.2: Add "Environment Variables" Section

- **Title:** Add "Environment Variables" Section to `DEVELOPMENT.md`
- **File(s) to Modify:** `DEVELOPMENT.md`
- **Reason:** The document lacks a section detailing the required environment variables.
- **Instructions:**
  1.  Add a new `## Environment Variables` section.
  2.  Create a markdown table listing all required environment variables (e.g., `NEXT_PUBLIC_SUPABASE_URL`, `NEXT_PUBLIC_SUPABASE_ANON_KEY`, `SUPABASE_SERVICE_ROLE_KEY`, `PLAID_CLIENT_ID`, `PLAID_SECRET`, `PLAID_ENV`).
  3.  Provide a brief description for each variable and where to find its value.
- **Mode:** `Intern`

### Subtask 2.3: Add "Technology Stack" Section

- **Title:** Add "Technology Stack" Section to `DEVELOPMENT.md`
- **File(s) to Modify:** `DEVELOPMENT.md`
- **Reason:** The document does not provide an overview of the project's technology stack.
- **Instructions:**
  1.  Add a new `## Technology Stack` section.
  2.  List the core technologies used in the project, such as:
      - Next.js
      - React
      - TypeScript
      - Tailwind CSS
      - Supabase
      - Plaid
      - Zod
      - React Hook Form
      - Jest & React Testing Library
- **Mode:** `Intern`

### Subtask 2.4: Update "Testing" Section

- **Title:** Update "Testing" Section in `DEVELOPMENT.md`
- **File(s) to Modify:** `DEVELOPMENT.md`
- **Reason:** The testing section is incomplete.
- **Instructions:**
  1.  Add a `## Testing` section if it doesn't exist, or update the existing one.
  2.  Provide the command to run all tests:
      ```bash
      npm test
      ```
  3.  Provide the command to run tests in watch mode:
      ```bash
      npm test -- --watch
      ```
- **Mode:** `Intern`

### Subtask 2.5: Add "Database" Section

- **Title:** Add "Database" Section to `DEVELOPMENT.md`
- **File(s) to Modify:** `DEVELOPMENT.md`
- **Reason:** The document is missing information about the database setup and migrations.
- **Instructions:**

  1.  Add a new `## Database` section.
  2.  Explain that the project uses Supabase for its database.
  3.  Provide instructions on how to apply local schema changes to the Supabase project using the Supabase CLI.

      ```bash
      # Link your local project to your Supabase project
      npx supabase link --project-ref <your-project-id>

      # Create a new migration file from schema changes
      npx supabase db diff -f <migration_name>

      # Apply all new local migrations to the remote database
      npx supabase db push
      ```

- **Mode:** `Junior`

### Subtask 2.6: Correct "Folder Structure" Section

- **Title:** Correct "Folder Structure" Section in `DEVELOPMENT.md`
- **File(s) to Modify:** `DEVELOPMENT.md`
- **Reason:** The folder structure description is outdated and inaccurate.
- **Instructions:**
  1.  Find the "Recommended Folder Structure" section.
  2.  Update the description to reflect the correct locations for Supabase and Plaid client files:
      - Change `lib/supabase.ts` to `src/lib/supabase/client.ts` and `src/lib/supabase/server.ts`.
      - Change `lib/plaid.ts` to `src/lib/plaid.ts`.
  3.  Review the rest of the structure and make any other necessary corrections.
- **Mode:** `Intern`
