# [1.1.0](https://github.com/arpieper/navsync/compare/v1.0.0...v1.1.0) (2025-06-15)

### Features

- complete Jest 30 migration with test fixes ([3cbbd72](https://github.com/arpieper/navsync/commit/3cbbd721d7fff7aa4eae2dc1f037e25524955ce1))

# 1.0.0 (2025-06-15)

### Bug Fixes

- **plaid:** Resolve integration and user creation failures ([c380628](https://github.com/arpieper/navsync/commit/c3806281f5550fc90f8735220e85845531003ffd))
- Updated pre-commit ([8947042](https://github.com/arpieper/navsync/commit/8947042518c208d3f191771a332e7148dd053dcc))

### Features

- **core:** Implement and test categorization system ([4ba6715](https://github.com/arpieper/navsync/commit/4ba6715b5ec5479efd549efdfc95caf9b0b979a2))
- **plaid:** implement transaction sync and management UI ([e9df6c8](https://github.com/arpieper/navsync/commit/e9df6c83d02b6a0d77d22a8450b0ad8a7ebfb814)), closes [#2](https://github.com/arpieper/navsync/issues/2)

# Changelog

All notable changes to this project will be documented in this file.

This project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html) and uses [Conventional Commits](https://conventionalcommits.org/) for automated versioning.

## [Unreleased]

### Added

- Initial project setup with semantic-release automation
