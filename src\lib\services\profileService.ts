import { createSupabaseServerClient } from '@/lib/supabase/server';

export async function updateOnboardingStatus(userId: string): Promise<void> {
  const supabase = await createSupabaseServerClient();
  const { error } = await supabase
    .from('profiles')
    .update({ onboarding_completed: true })
    .eq('id', userId);

  if (error) {
    console.error('Error updating onboarding status:', error);
    throw new Error('Could not update onboarding status.');
  }
}
