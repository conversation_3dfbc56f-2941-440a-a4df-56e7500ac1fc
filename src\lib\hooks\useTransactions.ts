'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';

interface Transaction {
  id: string;
  amount: number;
  currency_code: string;
  transaction_date: string;
  authorized_date: string | null;
  posted_date: string | null;
  merchant_name: string | null;
  description: string;
  category_id: string | null;
  user_category_id: string | null;
  plaid_category: string[] | null;
  plaid_category_detailed: string[] | null;
  transaction_type: string;
  location: Record<string, unknown> | null;
  is_pending: boolean;
  is_recurring: boolean;
  status: string;
  tags: string[] | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
  financial_accounts: {
    id: string;
    account_name: string;
    institution_name: string;
    account_type: string;
    account_subtype: string;
    mask: string | null;
  };
}

interface PaginationInfo {
  currentPage: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface TransactionsResponse {
  transactions: Transaction[];
  pagination: PaginationInfo;
}

interface TransactionFilters {
  searchQuery?: string;
  categoryId?: string;
  startDate?: string;
  endDate?: string;
}

export const useTransactions = (filters?: TransactionFilters) => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);

  // Memoize filters to prevent unnecessary re-renders
  const memoizedFilters = useMemo(() => filters, [filters]);

  const fetchTransactions = useCallback(
    async (page: number = 1) => {
      setIsLoading(true);
      setError(null);

      try {
        // Build query parameters
        const params = new URLSearchParams({
          page: page.toString(),
          pageSize: '20',
        });

        // Add filter parameters if they exist
        if (memoizedFilters?.searchQuery) {
          params.append('searchQuery', memoizedFilters.searchQuery);
        }
        if (memoizedFilters?.categoryId) {
          params.append('categoryId', memoizedFilters.categoryId);
        }
        if (memoizedFilters?.startDate) {
          params.append('startDate', memoizedFilters.startDate);
        }
        if (memoizedFilters?.endDate) {
          params.append('endDate', memoizedFilters.endDate);
        }

        const response = await fetch(`/api/transactions/get?${params.toString()}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch transactions');
        }

        const data: TransactionsResponse = await response.json();
        setTransactions(data.transactions);
        setPagination(data.pagination);
        setCurrentPage(page);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      } finally {
        setIsLoading(false);
      }
    },
    [memoizedFilters]
  );

  useEffect(() => {
    // Reset to page 1 when filters change
    setCurrentPage(1);
    fetchTransactions(1);
  }, [fetchTransactions]);

  const handlePageChange = (newPage: number) => {
    if (pagination && newPage >= 1 && newPage <= pagination.totalPages) {
      setCurrentPage(newPage);
      fetchTransactions(newPage);
    }
  };

  const retryFetch = () => {
    fetchTransactions(currentPage);
  };

  return {
    transactions,
    pagination,
    isLoading,
    error,
    handlePageChange,
    retryFetch,
  };
};
