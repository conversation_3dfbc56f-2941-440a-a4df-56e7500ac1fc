import { POST } from '../exchange-public-token/route';
import { plaidClient } from '../../../../lib/plaid';
import { createSupabaseServerClient } from '../../../../lib/supabase/server';
import { NextRequest } from 'next/server';
import * as plaidService from '../../../../lib/services/plaidService';

jest.mock('../../../../lib/plaid', () => ({
  plaidClient: {
    itemPublicTokenExchange: jest.fn(),
  },
}));

jest.mock('../../../../lib/supabase/server', () => ({
  createSupabaseServerClient: jest.fn(),
}));

jest.mock('../../../../lib/services/plaidService', () => ({
  createFinancialAccount: jest.fn(),
}));

interface ExchangeTokenRequest {
  public_token: string;
  metadata: {
    institution: {
      institution_id: string;
      name: string;
    };
  };
}

describe('POST /api/plaid/exchange-public-token', () => {
  const mockRequestBody: ExchangeTokenRequest = {
    public_token: 'public-sandbox-test-token',
    metadata: {
      institution: {
        institution_id: 'ins_test',
        name: 'Test Bank',
      },
    },
  };

  const mockPlaidResponse = {
    data: {
      access_token: 'access-sandbox-test-token',
      item_id: 'test-item-id',
    },
  };

  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const createMockRequest = (body: ExchangeTokenRequest | Partial<ExchangeTokenRequest>) => {
    return {
      json: jest.fn().mockResolvedValue(body),
    } as unknown as NextRequest;
  };

  test('Success (200 OK)', async () => {
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: mockUser },
          error: null,
        }),
      },
    });

    (plaidClient.itemPublicTokenExchange as jest.Mock).mockResolvedValue(mockPlaidResponse);
    (plaidService.createFinancialAccount as jest.Mock).mockResolvedValue({ ok: true });

    const req = createMockRequest(mockRequestBody);
    const res = await POST(req);

    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.ok).toBe(true);

    expect(plaidClient.itemPublicTokenExchange).toHaveBeenCalledWith({
      public_token: mockRequestBody.public_token,
    });

    expect(plaidService.createFinancialAccount).toHaveBeenCalledWith({
      userId: mockUser.id,
      accessToken: mockPlaidResponse.data.access_token,
      itemId: mockPlaidResponse.data.item_id,
      institutionName: mockRequestBody.metadata.institution.name,
      institutionId: mockRequestBody.metadata.institution.institution_id,
    });
  });

  test('Unauthorized (401)', async () => {
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: null },
          error: null,
        }),
      },
    });

    const req = createMockRequest(mockRequestBody);
    const res = await POST(req);

    expect(res.status).toBe(401);
    const data = await res.json();
    expect(data.error).toBe('Unauthorized');
    expect(plaidClient.itemPublicTokenExchange).not.toHaveBeenCalled();
  });

  test('Unauthorized (401) - Auth Error', async () => {
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: null },
          error: new Error('Auth error'),
        }),
      },
    });

    const req = createMockRequest(mockRequestBody);
    const res = await POST(req);

    expect(res.status).toBe(401);
    const data = await res.json();
    expect(data.error).toBe('Unauthorized');
    expect(plaidClient.itemPublicTokenExchange).not.toHaveBeenCalled();
  });

  test('Missing Required Fields (400)', async () => {
    const invalidRequestBody = {
      public_token: 'test-token',
    };

    const req = createMockRequest(invalidRequestBody);
    const res = await POST(req);

    expect(res.status).toBe(400);
    const data = await res.json();
    expect(data.error).toBe('Missing required fields: public_token and metadata.institution');
    expect(createSupabaseServerClient).not.toHaveBeenCalled();
    expect(plaidClient.itemPublicTokenExchange).not.toHaveBeenCalled();
  });

  test('Missing Public Token (400)', async () => {
    const invalidRequestBody = {
      metadata: {
        institution: {
          institution_id: 'ins_test',
          name: 'Test Bank',
        },
      },
    };

    const req = createMockRequest(invalidRequestBody);
    const res = await POST(req);

    expect(res.status).toBe(400);
    const data = await res.json();
    expect(data.error).toBe('Missing required fields: public_token and metadata.institution');
    expect(createSupabaseServerClient).not.toHaveBeenCalled();
    expect(plaidClient.itemPublicTokenExchange).not.toHaveBeenCalled();
  });

  test('Plaid Client Error (500)', async () => {
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: mockUser },
          error: null,
        }),
      },
    });

    const plaidError = new Error('Plaid API Error');
    (plaidClient.itemPublicTokenExchange as jest.Mock).mockRejectedValue(plaidError);

    const req = createMockRequest(mockRequestBody);
    const res = await POST(req);

    expect(res.status).toBe(500);
    const data = await res.json();
    expect(data.error).toBe(plaidError.message);
    expect(plaidClient.itemPublicTokenExchange).toHaveBeenCalledWith({
      public_token: mockRequestBody.public_token,
    });
  });

  test('Database Service Error (500)', async () => {
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: mockUser },
          error: null,
        }),
      },
    });

    (plaidClient.itemPublicTokenExchange as jest.Mock).mockResolvedValue(mockPlaidResponse);

    const dbError = new Error('Failed to store account credentials');
    (plaidService.createFinancialAccount as jest.Mock).mockRejectedValue(dbError);

    const req = createMockRequest(mockRequestBody);
    const res = await POST(req);

    expect(res.status).toBe(500);
    const data = await res.json();
    expect(data.error).toBe(dbError.message);

    expect(plaidClient.itemPublicTokenExchange).toHaveBeenCalledWith({
      public_token: mockRequestBody.public_token,
    });

    expect(plaidService.createFinancialAccount).toHaveBeenCalled();
  });

  test('JSON Parsing Error (500)', async () => {
    const jsonError = new Error('Invalid JSON');
    const req = {
      json: jest.fn().mockRejectedValue(jsonError),
    } as unknown as NextRequest;

    const res = await POST(req);

    expect(res.status).toBe(500);
    const data = await res.json();
    expect(data.error).toBe(jsonError.message);
    expect(createSupabaseServerClient).not.toHaveBeenCalled();
    expect(plaidClient.itemPublicTokenExchange).not.toHaveBeenCalled();
  });
});
