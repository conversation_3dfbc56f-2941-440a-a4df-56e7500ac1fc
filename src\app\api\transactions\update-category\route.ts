import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  const supabase = await createSupabaseServerClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  let transactionId: string;
  let newCategoryId: string;

  try {
    const body = await request.json();
    transactionId = body.transactionId;
    newCategoryId = body.newCategoryId;

    if (!transactionId || !newCategoryId) {
      throw new Error('transactionId and newCategoryId are required');
    }
  } catch (e) {
    const error = e as Error;
    return NextResponse.json({ error: `Invalid request body: ${error.message}` }, { status: 400 });
  }

  const { error } = await supabase.rpc('update_transaction_category_manual', {
    transaction_id_arg: transactionId,
    new_category_id_arg: newCategoryId,
  });

  if (error) {
    console.error('Error updating transaction category:', error);
    // Check for specific error messages from the RPC function
    if (error.message.includes('permission') || error.message.includes('Forbidden')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    if (error.message.includes('not found')) {
      return NextResponse.json({ error: 'Transaction not found' }, { status: 404 });
    }
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }

  return new NextResponse(null, { status: 200 });
}
