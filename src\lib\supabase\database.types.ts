export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

export type Database = {
  public: {
    Tables: {
      budget_tracking: {
        Row: {
          allocated_amount: number;
          budget_id: string;
          category_id: string;
          created_at: string | null;
          id: string;
          percentage_used: number | null;
          remaining_amount: number | null;
          spent_amount: number;
          updated_at: string | null;
        };
        Insert: {
          allocated_amount?: number;
          budget_id: string;
          category_id: string;
          created_at?: string | null;
          id?: string;
          percentage_used?: number | null;
          remaining_amount?: number | null;
          spent_amount?: number;
          updated_at?: string | null;
        };
        Update: {
          allocated_amount?: number;
          budget_id?: string;
          category_id?: string;
          created_at?: string | null;
          id?: string;
          percentage_used?: number | null;
          remaining_amount?: number | null;
          spent_amount?: number;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'budget_tracking_budget_id_fkey';
            columns: ['budget_id'];
            isOneToOne: false;
            referencedRelation: 'budgets';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'budget_tracking_category_id_fkey';
            columns: ['category_id'];
            isOneToOne: false;
            referencedRelation: 'transaction_categories';
            referencedColumns: ['id'];
          },
        ];
      };
      budgets: {
        Row: {
          budget_period: string;
          created_at: string | null;
          currency_code: string | null;
          description: string | null;
          end_date: string;
          id: string;
          is_active: boolean | null;
          name: string;
          start_date: string;
          total_allocated_amount: number;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          budget_period: string;
          created_at?: string | null;
          currency_code?: string | null;
          description?: string | null;
          end_date: string;
          id?: string;
          is_active?: boolean | null;
          name: string;
          start_date: string;
          total_allocated_amount?: number;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          budget_period?: string;
          created_at?: string | null;
          currency_code?: string | null;
          description?: string | null;
          end_date?: string;
          id?: string;
          is_active?: boolean | null;
          name?: string;
          start_date?: string;
          total_allocated_amount?: number;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'budgets_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      financial_accounts: {
        Row: {
          account_name: string;
          account_subtype: string | null;
          account_type: string;
          available_balance: number | null;
          created_at: string | null;
          currency_code: string | null;
          current_balance: number | null;
          id: string;
          institution_id: string | null;
          institution_name: string;
          is_active: boolean | null;
          last_synced_at: string | null;
          mask: string | null;
          plaid_account_id: string;
          plaid_item_id: string;
          plaid_metadata: Json | null;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          account_name: string;
          account_subtype?: string | null;
          account_type: string;
          available_balance?: number | null;
          created_at?: string | null;
          currency_code?: string | null;
          current_balance?: number | null;
          id?: string;
          institution_id?: string | null;
          institution_name: string;
          is_active?: boolean | null;
          last_synced_at?: string | null;
          mask?: string | null;
          plaid_account_id: string;
          plaid_item_id: string;
          plaid_metadata?: Json | null;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          account_name?: string;
          account_subtype?: string | null;
          account_type?: string;
          available_balance?: number | null;
          created_at?: string | null;
          currency_code?: string | null;
          current_balance?: number | null;
          id?: string;
          institution_id?: string | null;
          institution_name?: string;
          is_active?: boolean | null;
          last_synced_at?: string | null;
          mask?: string | null;
          plaid_account_id?: string;
          plaid_item_id?: string;
          plaid_metadata?: Json | null;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'financial_accounts_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      profiles: {
        Row: {
          avatar_url: string | null;
          created_at: string | null;
          currency_preference: string | null;
          date_of_birth: string | null;
          email: string;
          full_name: string | null;
          id: string;
          language_preference: string | null;
          notification_preferences: Json | null;
          onboarding_completed: boolean | null;
          phone: string | null;
          privacy_settings: Json | null;
          terms_accepted_at: string | null;
          timezone: string | null;
          updated_at: string | null;
        };
        Insert: {
          avatar_url?: string | null;
          created_at?: string | null;
          currency_preference?: string | null;
          date_of_birth?: string | null;
          email: string;
          full_name?: string | null;
          id: string;
          language_preference?: string | null;
          notification_preferences?: Json | null;
          onboarding_completed?: boolean | null;
          phone?: string | null;
          privacy_settings?: Json | null;
          terms_accepted_at?: string | null;
          timezone?: string | null;
          updated_at?: string | null;
        };
        Update: {
          avatar_url?: string | null;
          created_at?: string | null;
          currency_preference?: string | null;
          date_of_birth?: string | null;
          email?: string;
          full_name?: string | null;
          id?: string;
          language_preference?: string | null;
          notification_preferences?: Json | null;
          onboarding_completed?: boolean | null;
          phone?: string | null;
          privacy_settings?: Json | null;
          terms_accepted_at?: string | null;
          timezone?: string | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      transaction_categories: {
        Row: {
          category_type: string;
          color: string | null;
          created_at: string | null;
          description: string | null;
          icon: string | null;
          id: string;
          is_active: boolean | null;
          name: string;
          parent_category_id: string | null;
          updated_at: string | null;
          user_id: string | null;
        };
        Insert: {
          category_type: string;
          color?: string | null;
          created_at?: string | null;
          description?: string | null;
          icon?: string | null;
          id?: string;
          is_active?: boolean | null;
          name: string;
          parent_category_id?: string | null;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Update: {
          category_type?: string;
          color?: string | null;
          created_at?: string | null;
          description?: string | null;
          icon?: string | null;
          id?: string;
          is_active?: boolean | null;
          name?: string;
          parent_category_id?: string | null;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'transaction_categories_parent_category_id_fkey';
            columns: ['parent_category_id'];
            isOneToOne: false;
            referencedRelation: 'transaction_categories';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'transaction_categories_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      transactions: {
        Row: {
          account_id: string;
          account_owner: string | null;
          amount: number;
          authorized_date: string | null;
          category_id: string | null;
          confidence_level: string | null;
          created_at: string | null;
          currency_code: string | null;
          description: string;
          id: string;
          is_pending: boolean | null;
          is_recurring: boolean | null;
          location: Json | null;
          merchant_name: string | null;
          notes: string | null;
          plaid_category: Json | null;
          plaid_category_detailed: string | null;
          plaid_metadata: Json | null;
          plaid_transaction_id: string;
          posted_date: string | null;
          status: string;
          tags: string[] | null;
          transaction_code: string | null;
          transaction_date: string;
          transaction_type: string | null;
          updated_at: string | null;
          user_category_id: string | null;
          user_id: string;
        };
        Insert: {
          account_id: string;
          account_owner?: string | null;
          amount: number;
          authorized_date?: string | null;
          category_id?: string | null;
          confidence_level?: string | null;
          created_at?: string | null;
          currency_code?: string | null;
          description: string;
          id?: string;
          is_pending?: boolean | null;
          is_recurring?: boolean | null;
          location?: Json | null;
          merchant_name?: string | null;
          notes?: string | null;
          plaid_category?: Json | null;
          plaid_category_detailed?: string | null;
          plaid_metadata?: Json | null;
          plaid_transaction_id: string;
          posted_date?: string | null;
          status?: string;
          tags?: string[] | null;
          transaction_code?: string | null;
          transaction_date: string;
          transaction_type?: string | null;
          updated_at?: string | null;
          user_category_id?: string | null;
          user_id: string;
        };
        Update: {
          account_id?: string;
          account_owner?: string | null;
          amount?: number;
          authorized_date?: string | null;
          category_id?: string | null;
          confidence_level?: string | null;
          created_at?: string | null;
          currency_code?: string | null;
          description?: string;
          id?: string;
          is_pending?: boolean | null;
          is_recurring?: boolean | null;
          location?: Json | null;
          merchant_name?: string | null;
          notes?: string | null;
          plaid_category?: Json | null;
          plaid_category_detailed?: string | null;
          plaid_metadata?: Json | null;
          plaid_transaction_id?: string;
          posted_date?: string | null;
          status?: string;
          tags?: string[] | null;
          transaction_code?: string | null;
          transaction_date?: string;
          transaction_type?: string | null;
          updated_at?: string | null;
          user_category_id?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'transactions_account_id_fkey';
            columns: ['account_id'];
            isOneToOne: false;
            referencedRelation: 'financial_accounts';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'transactions_category_id_fkey';
            columns: ['category_id'];
            isOneToOne: false;
            referencedRelation: 'transaction_categories';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'transactions_user_category_id_fkey';
            columns: ['user_category_id'];
            isOneToOne: false;
            referencedRelation: 'user_categories';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'transactions_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      user_categories: {
        Row: {
          base_category_id: string | null;
          color: string | null;
          created_at: string | null;
          description: string | null;
          icon: string | null;
          id: string;
          is_active: boolean | null;
          name: string;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          base_category_id?: string | null;
          color?: string | null;
          created_at?: string | null;
          description?: string | null;
          icon?: string | null;
          id?: string;
          is_active?: boolean | null;
          name: string;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          base_category_id?: string | null;
          color?: string | null;
          created_at?: string | null;
          description?: string | null;
          icon?: string | null;
          id?: string;
          is_active?: boolean | null;
          name?: string;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'user_categories_base_category_id_fkey';
            columns: ['base_category_id'];
            isOneToOne: false;
            referencedRelation: 'transaction_categories';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'user_categories_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, 'public'>];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
        Database[DefaultSchemaTableNameOrOptions['schema']]['Views'])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
      Database[DefaultSchemaTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    ? (DefaultSchema['Tables'] & DefaultSchema['Views'])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema['Enums'] | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions['schema']]['Enums']
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions['schema']]['Enums'][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema['Enums']
    ? DefaultSchema['Enums'][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema['CompositeTypes']
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes']
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes'][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema['CompositeTypes']
    ? DefaultSchema['CompositeTypes'][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  public: {
    Enums: {},
  },
} as const;
