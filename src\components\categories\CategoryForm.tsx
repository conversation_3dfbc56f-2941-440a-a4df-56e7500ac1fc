'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Category, CategoryFormData } from '@/lib/hooks/useCategories';

const PREDEFINED_COLORS = [
  '#ef4444',
  '#f97316',
  '#f59e0b',
  '#eab308',
  '#84cc16',
  '#22c55e',
  '#10b981',
  '#14b8a6',
  '#06b6d4',
  '#0ea5e9',
  '#3b82f6',
  '#6366f1',
  '#8b5cf6',
  '#a855f7',
  '#d946ef',
  '#ec4899',
  '#f43f5e',
];

const PREDEFINED_ICONS = [
  '🍔',
  '🛒',
  '⛽',
  '🏠',
  '💡',
  '🚗',
  '🎬',
  '👕',
  '💊',
  '🎓',
  '✈️',
  '🏥',
  '💰',
  '🎮',
  '📱',
  '☕',
  '🍕',
  '🎵',
  '📚',
  '🏋️',
];

interface CategoryFormProps {
  editingCategory: Category | null;
  isSubmitting: boolean;
  onSubmit: (data: CategoryFormData) => Promise<void>;
  onCancel: () => void;
}

export default function CategoryForm({
  editingCategory,
  isSubmitting,
  onSubmit,
  onCancel,
}: CategoryFormProps) {
  const form = useForm<CategoryFormData>({
    defaultValues: {
      name: editingCategory?.name || '',
      description: editingCategory?.description || '',
      color: editingCategory?.color || PREDEFINED_COLORS[0],
      icon: editingCategory?.icon || PREDEFINED_ICONS[0],
    },
  });

  // Reset form when editingCategory changes
  React.useEffect(() => {
    form.reset({
      name: editingCategory?.name || '',
      description: editingCategory?.description || '',
      color: editingCategory?.color || PREDEFINED_COLORS[0],
      icon: editingCategory?.icon || PREDEFINED_ICONS[0],
    });
  }, [editingCategory, form]);

  const handleSubmit = async (data: CategoryFormData) => {
    try {
      await onSubmit(data);
      if (!editingCategory) {
        form.reset();
      }
    } catch {
      // Error is handled by the hook
    }
  };

  const handleCancel = () => {
    form.reset();
    onCancel();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{editingCategory ? 'Edit Category' : 'Create New Category'}</CardTitle>
        <CardDescription>
          {editingCategory
            ? 'Update the details of your category'
            : 'Add a new custom spending category'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-4'>
            <FormField
              control={form.control}
              name='name'
              rules={{ required: 'Category name is required' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder='e.g., Coffee Shops' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Input placeholder='e.g., Daily coffee purchases' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='color'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Color</FormLabel>
                  <FormControl>
                    <div className='space-y-2'>
                      <div className='flex flex-wrap gap-2'>
                        {PREDEFINED_COLORS.map((color) => (
                          <button
                            key={color}
                            type='button'
                            className={`w-8 h-8 rounded-full border-2 ${
                              field.value === color ? 'border-gray-800' : 'border-gray-300'
                            }`}
                            style={{ backgroundColor: color }}
                            onClick={() => field.onChange(color)}
                          />
                        ))}
                      </div>
                      <Input
                        type='color'
                        value={field.value}
                        onChange={field.onChange}
                        className='w-20 h-10'
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='icon'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Icon</FormLabel>
                  <FormControl>
                    <div className='space-y-2'>
                      <div className='flex flex-wrap gap-2'>
                        {PREDEFINED_ICONS.map((icon) => (
                          <button
                            key={icon}
                            type='button'
                            className={`w-10 h-10 text-xl border rounded ${
                              field.value === icon
                                ? 'border-blue-500 bg-blue-50'
                                : 'border-gray-300 hover:border-gray-400'
                            }`}
                            onClick={() => field.onChange(icon)}
                          >
                            {icon}
                          </button>
                        ))}
                      </div>
                      <Input
                        placeholder='Or enter custom emoji'
                        value={field.value}
                        onChange={field.onChange}
                        className='w-32'
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='flex gap-2'>
              <Button type='submit' disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <LoadingSpinner />
                    {editingCategory ? 'Updating...' : 'Creating...'}
                  </>
                ) : editingCategory ? (
                  'Update Category'
                ) : (
                  'Create Category'
                )}
              </Button>
              {editingCategory && (
                <Button type='button' variant='outline' onClick={handleCancel}>
                  Cancel
                </Button>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
