import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import TransactionsList from '@/components/transactions/TransactionsList';
import { useTransactions } from '@/lib/hooks/useTransactions';

// Mock the custom hook
jest.mock('@/lib/hooks/useTransactions');

// Mock TransactionCard to simplify testing
jest.mock('@/components/transactions/TransactionCard', () => {
  return {
    __esModule: true,
    default: ({ transaction }: { transaction: { id: string } }) => (
      <div data-testid={`transaction-card-${transaction.id}`}>Transaction Card</div>
    ),
  };
});

// Mock LoadingSpinner
jest.mock('@/components/ui/LoadingSpinner', () => {
  return {
    __esModule: true,
    default: () => <div data-testid='loading-spinner'>Loading...</div>,
  };
});

const mockUseTransactions = useTransactions as jest.Mock;

describe('TransactionsList', () => {
  test('Test Case 1: Successful Data Fetch and Rendering', async () => {
    mockUseTransactions.mockReturnValue({
      transactions: [{ id: '1' }, { id: '2' }],
      pagination: {
        currentPage: 1,
        pageSize: 2,
        totalCount: 5,
        totalPages: 3,
        hasNextPage: true,
        hasPreviousPage: false,
      },
      isLoading: false,
      error: null,
      handlePageChange: jest.fn(),
      retryFetch: jest.fn(),
    });

    render(<TransactionsList />);

    await waitFor(() => {
      expect(screen.getAllByText('Transaction Card')).toHaveLength(2);
    });

    expect(screen.getByText('Showing 1 to 2 of 5 transactions')).toBeInTheDocument();
    expect(screen.getByText('Page 1 of 3')).toBeInTheDocument();
  });

  test('Test Case 2: Loading State', async () => {
    mockUseTransactions.mockReturnValue({
      transactions: [],
      pagination: null,
      isLoading: true,
      error: null,
      handlePageChange: jest.fn(),
      retryFetch: jest.fn(),
    });

    render(<TransactionsList />);

    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    expect(screen.getByText('Loading transactions...')).toBeInTheDocument();
  });

  test('Test Case 3: Error State', async () => {
    const errorMessage = 'Network error';
    mockUseTransactions.mockReturnValue({
      transactions: [],
      pagination: null,
      isLoading: false,
      error: errorMessage,
      handlePageChange: jest.fn(),
      retryFetch: jest.fn(),
    });

    render(<TransactionsList />);

    await waitFor(() => {
      expect(screen.getByText('Error Loading Transactions')).toBeInTheDocument();
    });
    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  test('Test Case 4: Empty State', async () => {
    mockUseTransactions.mockReturnValue({
      transactions: [],
      pagination: {
        totalCount: 0,
      },
      isLoading: false,
      error: null,
      handlePageChange: jest.fn(),
      retryFetch: jest.fn(),
    });

    render(<TransactionsList />);

    await waitFor(() => {
      expect(screen.getByText('No Transactions Found')).toBeInTheDocument();
    });
  });

  test('Test Case 5: Pagination Interaction', async () => {
    const handlePageChange = jest.fn();
    mockUseTransactions.mockReturnValue({
      transactions: [{ id: '1' }],
      pagination: {
        currentPage: 1,
        pageSize: 1,
        totalCount: 2,
        totalPages: 2,
        hasNextPage: true,
        hasPreviousPage: false,
      },
      isLoading: false,
      error: null,
      handlePageChange,
      retryFetch: jest.fn(),
    });

    render(<TransactionsList />);

    await waitFor(() => {
      expect(screen.getByText('Showing 1 to 1 of 2 transactions')).toBeInTheDocument();
    });

    const nextButton = screen.getByRole('button', { name: /next/i });
    expect(nextButton).not.toBeDisabled();

    fireEvent.click(nextButton);

    expect(handlePageChange).toHaveBeenCalledWith(2);
  });

  test('Test Case 6: Retry Fetch on Error', async () => {
    const retryFetch = jest.fn();
    const errorMessage = 'Failed to load';
    mockUseTransactions.mockReturnValue({
      transactions: [],
      pagination: null,
      isLoading: false,
      error: errorMessage,
      handlePageChange: jest.fn(),
      retryFetch,
    });

    render(<TransactionsList />);

    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });

    const retryButton = screen.getByRole('button', { name: /try again/i });
    fireEvent.click(retryButton);

    expect(retryFetch).toHaveBeenCalledTimes(1);
  });
});
